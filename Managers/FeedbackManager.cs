using Mapster;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Cos;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Services.Interfaces;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;
using Microsoft.Extensions.Logging;

namespace AcademicPerformance.Managers
{
    /// <summary>
    /// Submission feedback, revision request ve feedback history operasyonları
    /// </summary>
    public class FeedbackManager : IFeedbackManager
    {
        private readonly IFeedbackStore _feedbackStore;
        private readonly ISubmissionStore _submissionStore;
        private readonly IAcademicianStore _academicianStore;
        private readonly IFormStore _formStore;
        private readonly IUserDataService _userDataService;
        private readonly ILogger<FeedbackManager> _logger;

        // Feedback type constants
        private const string FeedbackTypeApproval = "Approval";
        private const string FeedbackTypeRejection = "Rejection";
        private const string FeedbackTypeRevisionRequest = "RevisionRequest";

        // Status constants
        private const string StatusSubmitted = "Submitted";
        private const string StatusUnderReview = "UnderReview";
        private const string StatusApproved = "Approved";
        private const string StatusRejected = "Rejected";
        private const string StatusRequiresRevision = "RequiresRevision";

        public FeedbackManager(
            IFeedbackStore feedbackStore,
            ISubmissionStore submissionStore,
            IAcademicianStore academicianStore,
            IFormStore formStore,
            IUserDataService userDataService,
            ILogger<FeedbackManager> logger)
        {
            _feedbackStore = feedbackStore;
            _submissionStore = submissionStore;
            _academicianStore = academicianStore;
            _formStore = formStore;
            _userDataService = userDataService;
            _logger = logger;
        }

        #region Feedback History Operations

        /// <summary>
        /// Submission için tüm feedback geçmişini getir
        /// </summary>
        public async Task<FeedbackHistoryDto> GetFeedbackHistoryAsync(string submissionId, string requestingUserId)
        {
            try
            {
                _logger.LogInformation("Getting feedback history for submission {SubmissionId} by user {UserId}", submissionId, requestingUserId);

                // Authorization kontrolü
                if (!await CanViewFeedbackAsync(requestingUserId, submissionId))
                {
                    throw new UnauthorizedAccessException("User does not have permission to view feedback for this submission");
                }

                // Submission bilgilerini getir
                var submission = await _submissionStore.GetSubmissionByIdAsync(submissionId);
                if (submission == null)
                {
                    throw new ArgumentException("Submission not found", nameof(submissionId));
                }

                // Feedback'leri getir
                var feedbackEntities = await _feedbackStore.GetFeedbacksBySubmissionIdAsync(submissionId);

                // Akademisyen bilgilerini getir
                var academicianProfile = await _academicianStore.GetAcademicianProfileByUniversityUserIdAsync(submission.AcademicianUserId);
                var academicianName = academicianProfile?.Name ?? "Unknown";

                // Form bilgilerini getir
                var form = await _formStore.GetEvaluationFormByIdAsync(submission.FormId);
                var formName = form?.Name ?? "Unknown Form";

                // DTO'ya dönüştür
                var feedbackHistory = new FeedbackHistoryDto
                {
                    SubmissionId = submissionId,
                    AcademicianName = academicianName,
                    FormName = formName,
                    CurrentStatus = submission.Status,
                    LastFeedbackDate = feedbackEntities.Any() ? feedbackEntities.Max(f => f.CreatedAt) : null,
                    FeedbackEntries = feedbackEntities.Adapt<List<FeedbackEntryDto>>()
                };

                // Controller isimlerini doldur
                foreach (var entry in feedbackHistory.FeedbackEntries)
                {
                    var controllerData = await _userDataService.GetUserProfileAsync(entry.ControllerUserId);
                    entry.ControllerName = controllerData?.FullName ?? "Unknown Controller";
                }

                _logger.LogInformation("Successfully retrieved feedback history for submission {SubmissionId}", submissionId);
                return feedbackHistory;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting feedback history for submission {SubmissionId}", submissionId);
                throw;
            }
        }

        /// <summary>
        /// Akademisyen için tüm submission'ların feedback özetini getir
        /// </summary>
        public async Task<PagedListDto<FeedbackSummaryDto>> GetAcademicianFeedbackSummaryAsync(string academicianUserId, PagedListCo<FeedbackFilterCo> co)
        {
            try
            {
                _logger.LogInformation("Getting feedback summary for academician {AcademicianUserId}", academicianUserId);

                var pagedFeedbacks = await _feedbackStore.GetFeedbacksByAcademicianAsync(academicianUserId, co);

                // Her submission için özet bilgileri hesapla
                var summaries = new List<FeedbackSummaryDto>();

                // Burada feedback'leri submission'lara göre grupla ve özet oluştur
                // Implementation detayları FeedbackStore'da yapılacak

                return new PagedListDto<FeedbackSummaryDto>
                {
                    Data = summaries,
                    TotalCount = pagedFeedbacks.TotalCount,
                    Page = co.Pager.Page,
                    Size = co.Pager.Size
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting feedback summary for academician {AcademicianUserId}", academicianUserId);
                throw;
            }
        }

        /// <summary>
        /// Controller için verdiği feedback'lerin özetini getir
        /// </summary>
        public async Task<PagedListDto<FeedbackSummaryDto>> GetControllerFeedbackSummaryAsync(string controllerId, PagedListCo<FeedbackFilterCo> co)
        {
            try
            {
                _logger.LogInformation("Getting feedback summary for controller {ControllerId}", controllerId);

                var pagedFeedbacks = await _feedbackStore.GetFeedbacksByControllerAsync(controllerId, co);

                // Controller'ın verdiği feedback'lerin özetini oluştur
                var summaries = new List<FeedbackSummaryDto>();

                // Implementation detayları FeedbackStore'da yapılacak

                return new PagedListDto<FeedbackSummaryDto>
                {
                    Data = summaries,
                    TotalCount = pagedFeedbacks.TotalCount,
                    Page = co.Pager.Page,
                    Size = co.Pager.Size
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting feedback summary for controller {ControllerId}", controllerId);
                throw;
            }
        }

        #endregion

        #region Approval Feedback Operations

        /// <summary>
        /// Gelişmiş approval feedback oluştur
        /// </summary>
        public async Task<FeedbackEntryDto> CreateApprovalFeedbackAsync(ApprovalFeedbackDto feedbackDto, string controllerId)
        {
            try
            {
                _logger.LogInformation("Creating approval feedback for submission {SubmissionId} by controller {ControllerId}",
                    feedbackDto.SubmissionId, controllerId);

                // Validation
                if (!await CanCreateFeedbackAsync(controllerId, feedbackDto.SubmissionId, FeedbackTypeApproval))
                {
                    throw new UnauthorizedAccessException("Controller does not have permission to create approval feedback for this submission");
                }

                if (!await CanReceiveFeedbackAsync(feedbackDto.SubmissionId, FeedbackTypeApproval))
                {
                    throw new InvalidOperationException("Submission cannot receive approval feedback in its current state");
                }

                // Submission bilgilerini getir
                var submission = await _submissionStore.GetSubmissionByIdAsync(feedbackDto.SubmissionId);
                if (submission == null)
                {
                    throw new ArgumentException("Submission not found", nameof(feedbackDto.SubmissionId));
                }

                // Feedback entity oluştur
                var feedbackEntity = new SubmissionFeedbackEntity
                {
                    Id = Guid.NewGuid().ToString(),
                    SubmissionId = feedbackDto.SubmissionId,
                    FeedbackType = FeedbackTypeApproval,
                    ControllerUserId = controllerId,
                    Message = feedbackDto.Comments ?? "Submission approved",
                    SubmissionStatusAtTime = submission.Status,
                    OverallRating = feedbackDto.OverallRating,
                    Highlights = feedbackDto.Highlights,
                    FutureSuggestions = feedbackDto.FutureSuggestions,
                    CreatedAt = DateTime.UtcNow,
                    CreatedByUserId = controllerId
                };

                // Feedback'i kaydet
                var createdFeedback = await _feedbackStore.CreateFeedbackAsync(feedbackEntity);

                // Kriter bazında approval'ları kaydet
                if (feedbackDto.CriterionApprovals?.Any() == true)
                {
                    var criterionFeedbacks = feedbackDto.CriterionApprovals.Select(ca => new CriterionFeedbackEntity
                    {
                        Id = Guid.NewGuid().ToString(),
                        SubmissionFeedbackId = createdFeedback.Id,
                        CriterionLinkId = ca.CriterionId,
                        FeedbackMessage = ca.Comment ?? "Approved",
                        Status = ca.Status,
                        Rating = ca.Rating,
                        CreatedAt = DateTime.UtcNow,
                        CreatedByUserId = controllerId
                    }).ToList();

                    await _feedbackStore.CreateBulkCriterionFeedbackAsync(criterionFeedbacks);
                }

                // Submission status'unu güncelle
                await _submissionStore.UpdateSubmissionStatusAsync(feedbackDto.SubmissionId, StatusApproved, DateTime.UtcNow);

                // DTO'ya dönüştür
                var result = createdFeedback.Adapt<FeedbackEntryDto>();

                // Controller adını doldur
                var controllerData = await _userDataService.GetUserProfileAsync(controllerId);
                result.ControllerName = controllerData?.FullName ?? "Unknown Controller";

                _logger.LogInformation("Successfully created approval feedback {FeedbackId} for submission {SubmissionId}",
                    createdFeedback.Id, feedbackDto.SubmissionId);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating approval feedback for submission {SubmissionId}", feedbackDto.SubmissionId);
                throw;
            }
        }

        /// <summary>
        /// Approval feedback'i güncelle
        /// </summary>
        public async Task<bool> UpdateApprovalFeedbackAsync(string feedbackId, ApprovalFeedbackDto feedbackDto, string controllerId)
        {
            try
            {
                _logger.LogInformation("Updating approval feedback {FeedbackId} by controller {ControllerId}", feedbackId, controllerId);

                // Authorization kontrolü
                if (!await CanEditFeedbackAsync(feedbackId, controllerId))
                {
                    throw new UnauthorizedAccessException("Controller does not have permission to edit this feedback");
                }

                // Mevcut feedback'i getir
                var existingFeedback = await _feedbackStore.GetFeedbackByIdAsync(feedbackId);
                if (existingFeedback == null)
                {
                    throw new ArgumentException("Feedback not found", nameof(feedbackId));
                }

                // Güncelle
                existingFeedback.Message = feedbackDto.Comments ?? existingFeedback.Message;
                existingFeedback.OverallRating = feedbackDto.OverallRating;
                existingFeedback.Highlights = feedbackDto.Highlights;
                existingFeedback.FutureSuggestions = feedbackDto.FutureSuggestions;
                existingFeedback.UpdatedAt = DateTime.UtcNow;
                existingFeedback.UpdatedByUserId = controllerId;

                var result = await _feedbackStore.UpdateFeedbackAsync(existingFeedback);

                _logger.LogInformation("Successfully updated approval feedback {FeedbackId}", feedbackId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating approval feedback {FeedbackId}", feedbackId);
                throw;
            }
        }

        #endregion

        #region Revision Request Operations

        /// <summary>
        /// Revision request oluştur
        /// </summary>
        public async Task<FeedbackEntryDto> CreateRevisionRequestAsync(RevisionRequestDto revisionDto, string controllerId)
        {
            try
            {
                _logger.LogInformation("Creating revision request for submission {SubmissionId} by controller {ControllerId}",
                    revisionDto.SubmissionId, controllerId);

                // Validation
                if (!await CanCreateFeedbackAsync(controllerId, revisionDto.SubmissionId, FeedbackTypeRevisionRequest))
                {
                    throw new UnauthorizedAccessException("Controller does not have permission to create revision request for this submission");
                }

                if (!await CanReceiveFeedbackAsync(revisionDto.SubmissionId, FeedbackTypeRevisionRequest))
                {
                    throw new InvalidOperationException("Submission cannot receive revision request in its current state");
                }

                // Submission bilgilerini getir
                var submission = await _submissionStore.GetSubmissionByIdAsync(revisionDto.SubmissionId);
                if (submission == null)
                {
                    throw new ArgumentException("Submission not found", nameof(revisionDto.SubmissionId));
                }

                // Feedback entity oluştur
                var feedbackEntity = new SubmissionFeedbackEntity
                {
                    Id = Guid.NewGuid().ToString(),
                    SubmissionId = revisionDto.SubmissionId,
                    FeedbackType = FeedbackTypeRevisionRequest,
                    ControllerUserId = controllerId,
                    Message = revisionDto.GeneralMessage,
                    SubmissionStatusAtTime = submission.Status,
                    RevisionDeadline = revisionDto.RevisionDeadline,
                    Priority = revisionDto.Priority,
                    CreatedAt = DateTime.UtcNow,
                    CreatedByUserId = controllerId
                };

                // Feedback'i kaydet
                var createdFeedback = await _feedbackStore.CreateFeedbackAsync(feedbackEntity);

                // Kriter bazında revision request'leri kaydet
                if (revisionDto.CriterionRevisions?.Any() == true)
                {
                    var criterionFeedbacks = revisionDto.CriterionRevisions.Select(cr => new CriterionFeedbackEntity
                    {
                        Id = Guid.NewGuid().ToString(),
                        SubmissionFeedbackId = createdFeedback.Id,
                        CriterionLinkId = cr.CriterionId,
                        FeedbackMessage = cr.RevisionMessage,
                        Status = "NeedsRevision",
                        RevisionType = cr.RevisionType,
                        IsRequired = cr.IsRequired,
                        CreatedAt = DateTime.UtcNow,
                        CreatedByUserId = controllerId
                    }).ToList();

                    await _feedbackStore.CreateBulkCriterionFeedbackAsync(criterionFeedbacks);
                }

                // Submission status'unu güncelle
                await _submissionStore.UpdateSubmissionStatusAsync(revisionDto.SubmissionId, StatusRequiresRevision);

                // DTO'ya dönüştür
                var result = createdFeedback.Adapt<FeedbackEntryDto>();

                // Controller adını doldur
                var controllerData = await _userDataService.GetUserProfileAsync(controllerId);
                result.ControllerName = controllerData?.FullName ?? "Unknown Controller";

                _logger.LogInformation("Successfully created revision request {FeedbackId} for submission {SubmissionId}",
                    createdFeedback.Id, revisionDto.SubmissionId);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating revision request for submission {SubmissionId}", revisionDto.SubmissionId);
                throw;
            }
        }

        /// <summary>
        /// Revision request'i güncelle
        /// </summary>
        public async Task<bool> UpdateRevisionRequestAsync(string feedbackId, RevisionRequestDto revisionDto, string controllerId)
        {
            try
            {
                _logger.LogInformation("Updating revision request {FeedbackId} by controller {ControllerId}", feedbackId, controllerId);

                // Authorization kontrolü
                if (!await CanEditFeedbackAsync(feedbackId, controllerId))
                {
                    throw new UnauthorizedAccessException("Controller does not have permission to edit this feedback");
                }

                // Mevcut feedback'i getir
                var existingFeedback = await _feedbackStore.GetFeedbackByIdAsync(feedbackId);
                if (existingFeedback == null)
                {
                    throw new ArgumentException("Feedback not found", nameof(feedbackId));
                }

                // Güncelle
                existingFeedback.Message = revisionDto.GeneralMessage;
                existingFeedback.RevisionDeadline = revisionDto.RevisionDeadline;
                existingFeedback.Priority = revisionDto.Priority;
                existingFeedback.UpdatedAt = DateTime.UtcNow;
                existingFeedback.UpdatedByUserId = controllerId;

                var result = await _feedbackStore.UpdateFeedbackAsync(existingFeedback);

                _logger.LogInformation("Successfully updated revision request {FeedbackId}", feedbackId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating revision request {FeedbackId}", feedbackId);
                throw;
            }
        }

        /// <summary>
        /// Revision request'e akademisyen yanıtını kaydet
        /// </summary>
        public async Task<bool> RespondToRevisionRequestAsync(string feedbackId, string academicianResponse, string academicianUserId)
        {
            try
            {
                _logger.LogInformation("Academician {AcademicianUserId} responding to revision request {FeedbackId}",
                    academicianUserId, feedbackId);

                // Feedback'i getir
                var feedback = await _feedbackStore.GetFeedbackByIdAsync(feedbackId);
                if (feedback == null)
                {
                    throw new ArgumentException("Feedback not found", nameof(feedbackId));
                }

                // Authorization kontrolü - akademisyen kendi submission'ına yanıt verebilir
                var submission = await _submissionStore.GetSubmissionByIdAsync(feedback.SubmissionId);
                if (submission?.AcademicianUserId != academicianUserId)
                {
                    throw new UnauthorizedAccessException("Academician can only respond to their own revision requests");
                }

                // Yanıtı kaydet
                feedback.AcademicianResponse = academicianResponse;
                feedback.AcademicianResponseDate = DateTime.UtcNow;
                feedback.UpdatedAt = DateTime.UtcNow;
                feedback.UpdatedByUserId = academicianUserId;

                var result = await _feedbackStore.UpdateFeedbackAsync(feedback);

                _logger.LogInformation("Successfully recorded academician response to revision request {FeedbackId}", feedbackId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error recording academician response to revision request {FeedbackId}", feedbackId);
                throw;
            }
        }

        /// <summary>
        /// Aktif revision request'leri getir
        /// </summary>
        public async Task<List<FeedbackEntryDto>> GetActiveRevisionRequestsAsync(string academicianUserId)
        {
            try
            {
                _logger.LogInformation("Getting active revision requests for academician {AcademicianUserId}", academicianUserId);

                var activeRevisions = await _feedbackStore.GetActiveRevisionRequestsAsync(academicianUserId);
                var result = activeRevisions.Adapt<List<FeedbackEntryDto>>();

                // Controller isimlerini doldur
                foreach (var entry in result)
                {
                    var controllerData = await _userDataService.GetUserProfileAsync(entry.ControllerUserId);
                    entry.ControllerName = controllerData?.FullName ?? "Unknown Controller";
                }

                _logger.LogInformation("Successfully retrieved {Count} active revision requests for academician {AcademicianUserId}",
                    result.Count, academicianUserId);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active revision requests for academician {AcademicianUserId}", academicianUserId);
                throw;
            }
        }

        #endregion

        #region Rejection Feedback Operations

        /// <summary>
        /// Rejection feedback oluştur (mevcut SubmissionRejectionDto ile uyumlu)
        /// </summary>
        public async Task<FeedbackEntryDto> CreateRejectionFeedbackAsync(string submissionId, string comments, string controllerId)
        {
            try
            {
                _logger.LogInformation("Creating rejection feedback for submission {SubmissionId} by controller {ControllerId}",
                    submissionId, controllerId);

                // Validation
                if (!await CanCreateFeedbackAsync(controllerId, submissionId, FeedbackTypeRejection))
                {
                    throw new UnauthorizedAccessException("Controller does not have permission to create rejection feedback for this submission");
                }

                if (!await CanReceiveFeedbackAsync(submissionId, FeedbackTypeRejection))
                {
                    throw new InvalidOperationException("Submission cannot receive rejection feedback in its current state");
                }

                // Submission bilgilerini getir
                var submission = await _submissionStore.GetSubmissionByIdAsync(submissionId);
                if (submission == null)
                {
                    throw new ArgumentException("Submission not found", nameof(submissionId));
                }

                // Feedback entity oluştur
                var feedbackEntity = new SubmissionFeedbackEntity
                {
                    Id = Guid.NewGuid().ToString(),
                    SubmissionId = submissionId,
                    FeedbackType = FeedbackTypeRejection,
                    ControllerUserId = controllerId,
                    Message = comments,
                    SubmissionStatusAtTime = submission.Status,
                    CreatedAt = DateTime.UtcNow,
                    CreatedByUserId = controllerId
                };

                // Feedback'i kaydet
                var createdFeedback = await _feedbackStore.CreateFeedbackAsync(feedbackEntity);

                // Submission status'unu güncelle
                await _submissionStore.UpdateSubmissionStatusAsync(submissionId, StatusRejected, DateTime.UtcNow);

                // DTO'ya dönüştür
                var result = createdFeedback.Adapt<FeedbackEntryDto>();

                // Controller adını doldur
                var controllerData = await _userDataService.GetUserProfileAsync(controllerId);
                result.ControllerName = controllerData?.FullName ?? "Unknown Controller";

                _logger.LogInformation("Successfully created rejection feedback {FeedbackId} for submission {SubmissionId}",
                    createdFeedback.Id, submissionId);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating rejection feedback for submission {SubmissionId}", submissionId);
                throw;
            }
        }

        #endregion

        #region Validation and Authorization

        /// <summary>
        /// Kullanıcının feedback oluşturma yetkisi var mı kontrol et
        /// </summary>
        public async Task<bool> CanCreateFeedbackAsync(string userId, string submissionId, string feedbackType)
        {
            try
            {
                // Submission var mı kontrol et
                var submission = await _submissionStore.GetSubmissionByIdAsync(submissionId);
                if (submission == null) return false;

                // Kullanıcı controller rolünde mi kontrol et (basit implementasyon)
                // Gerçek implementasyonda role kontrolü yapılacak

                // Submission durumu uygun mu kontrol et
                return await CanReceiveFeedbackAsync(submissionId, feedbackType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking feedback creation permission for user {UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// Kullanıcının feedback görüntüleme yetkisi var mı kontrol et
        /// </summary>
        public async Task<bool> CanViewFeedbackAsync(string userId, string submissionId)
        {
            try
            {
                // Submission var mı kontrol et
                var submission = await _submissionStore.GetSubmissionByIdAsync(submissionId);
                if (submission == null) return false;

                // Kullanıcı submission'ın sahibi mi veya controller mi kontrol et
                // Basit implementasyon - gerçek implementasyonda role kontrolü yapılacak
                return submission.AcademicianUserId == userId || true; // Controller kontrolü eklenecek
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking feedback view permission for user {UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// Feedback'in düzenlenebilir olup olmadığını kontrol et
        /// </summary>
        public async Task<bool> CanEditFeedbackAsync(string feedbackId, string userId)
        {
            try
            {
                var feedback = await _feedbackStore.GetFeedbackByIdAsync(feedbackId);
                if (feedback == null) return false;

                // Sadece feedback'i oluşturan controller düzenleyebilir
                // Ve feedback 24 saat içinde oluşturulmuş olmalı
                return feedback.ControllerUserId == userId &&
                       DateTime.UtcNow.Subtract(feedback.CreatedAt).TotalHours <= 24;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking feedback edit permission for user {UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// Submission'ın feedback alabilir durumda olup olmadığını kontrol et
        /// </summary>
        public async Task<bool> CanReceiveFeedbackAsync(string submissionId, string feedbackType)
        {
            try
            {
                var submission = await _submissionStore.GetSubmissionByIdAsync(submissionId);
                if (submission == null) return false;

                // Status kontrolü
                switch (feedbackType)
                {
                    case FeedbackTypeApproval:
                        return submission.Status == StatusSubmitted || submission.Status == StatusUnderReview;
                    case FeedbackTypeRejection:
                        return submission.Status == StatusSubmitted || submission.Status == StatusUnderReview;
                    case FeedbackTypeRevisionRequest:
                        return submission.Status == StatusSubmitted || submission.Status == StatusUnderReview;
                    default:
                        return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if submission can receive feedback");
                return false;
            }
        }

        #endregion

        #region Statistics and Analytics

        /// <summary>
        /// Feedback istatistiklerini getir (dashboard için)
        /// </summary>
        public async Task<FeedbackSummaryDto> GetFeedbackStatisticsAsync(string userId, string userRole, DateRange? dateRange = null)
        {
            try
            {
                _logger.LogInformation("Getting feedback statistics for user {UserId} with role {UserRole}", userId, userRole);

                var stats = await _feedbackStore.GetFeedbackStatisticsAsync(userId, userRole, dateRange);

                return new FeedbackSummaryDto
                {
                    TotalFeedbacks = (int)(stats.GetValueOrDefault("TotalFeedbacks", 0)),
                    ApprovedCount = (int)(stats.GetValueOrDefault("ApprovedCount", 0)),
                    RejectedCount = (int)(stats.GetValueOrDefault("RejectedCount", 0)),
                    RevisionRequestedCount = (int)(stats.GetValueOrDefault("RevisionRequestedCount", 0)),
                    AverageFeedbackTimeHours = (double)(stats.GetValueOrDefault("AverageFeedbackTimeHours", 0.0)),
                    FeedbacksThisMonth = (int)(stats.GetValueOrDefault("FeedbacksThisMonth", 0)),
                    PendingFeedbacks = (int)(stats.GetValueOrDefault("PendingFeedbacks", 0))
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting feedback statistics for user {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// Ortalama feedback süresini hesapla
        /// </summary>
        public async Task<double> CalculateAverageFeedbackTimeAsync(string? controllerId = null, DateRange? dateRange = null)
        {
            try
            {
                _logger.LogInformation("Calculating average feedback time for controller {ControllerId}", controllerId ?? "All");

                return await _feedbackStore.CalculateAverageFeedbackTimeAsync(controllerId, dateRange);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating average feedback time");
                throw;
            }
        }

        /// <summary>
        /// En çok revision talep edilen kriterleri getir
        /// </summary>
        public async Task<List<CriterionRevisionStatDto>> GetMostRevisedCriteriaAsync(int limit = 10, DateRange? dateRange = null)
        {
            try
            {
                _logger.LogInformation("Getting most revised criteria with limit {Limit}", limit);

                var stats = await _feedbackStore.GetMostRevisedCriteriaAsync(limit, dateRange);

                return stats.Select(s => new CriterionRevisionStatDto
                {
                    CriterionId = s.GetValueOrDefault("CriterionId", "").ToString() ?? "",
                    CriterionName = s.GetValueOrDefault("CriterionName", "").ToString() ?? "",
                    RevisionCount = (int)(s.GetValueOrDefault("RevisionCount", 0)),
                    RevisionPercentage = (double)(s.GetValueOrDefault("RevisionPercentage", 0.0))
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting most revised criteria");
                throw;
            }
        }

        #endregion

        #region Notification and Communication

        /// <summary>
        /// Feedback notification gönder
        /// </summary>
        public async Task<bool> SendFeedbackNotificationAsync(string feedbackId, string notificationType)
        {
            try
            {
                _logger.LogInformation("Sending feedback notification for feedback {FeedbackId}", feedbackId);

                // Feedback bilgilerini getir
                var feedback = await _feedbackStore.GetFeedbackByIdAsync(feedbackId);
                if (feedback == null) return false;

                // Submission bilgilerini getir
                var submission = await _submissionStore.GetSubmissionByIdAsync(feedback.SubmissionId);
                if (submission == null) return false;

                // Notification log oluştur
                var notificationLog = new FeedbackNotificationLogEntity
                {
                    Id = Guid.NewGuid().ToString(),
                    SubmissionFeedbackId = feedbackId,
                    NotificationType = notificationType,
                    RecipientUserId = submission.AcademicianUserId,
                    Subject = $"Feedback Received for Your Submission",
                    Content = $"You have received {feedback.FeedbackType} feedback for your submission.",
                    Status = "Pending",
                    CreatedAt = DateTime.UtcNow
                };

                await _feedbackStore.CreateNotificationLogAsync(notificationLog);

                // Feedback'i notification sent olarak işaretle
                feedback.NotificationSent = true;
                feedback.NotificationSentAt = DateTime.UtcNow;
                await _feedbackStore.UpdateFeedbackAsync(feedback);

                _logger.LogInformation("Successfully created notification for feedback {FeedbackId}", feedbackId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending feedback notification for feedback {FeedbackId}", feedbackId);
                return false;
            }
        }

        /// <summary>
        /// Deadline yaklaşan revision request'ler için reminder gönder
        /// </summary>
        public async Task<int> SendRevisionDeadlineRemindersAsync(int daysBeforeDeadline = 3)
        {
            try
            {
                _logger.LogInformation("Sending revision deadline reminders for {Days} days before deadline", daysBeforeDeadline);

                var upcomingRevisions = await _feedbackStore.GetUpcomingDeadlineRevisionRequestsAsync(daysBeforeDeadline);
                int sentCount = 0;

                foreach (var revision in upcomingRevisions)
                {
                    var success = await SendFeedbackNotificationAsync(revision.Id, "DeadlineReminder");
                    if (success) sentCount++;
                }

                _logger.LogInformation("Successfully sent {Count} revision deadline reminders", sentCount);
                return sentCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending revision deadline reminders");
                return 0;
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Feedback türünün geçerli olup olmadığını kontrol et
        /// </summary>
        public async Task<bool> IsValidFeedbackTypeAsync(string feedbackType)
        {
            var validTypes = new[] { FeedbackTypeApproval, FeedbackTypeRejection, FeedbackTypeRevisionRequest };
            return await Task.FromResult(validTypes.Contains(feedbackType));
        }

        /// <summary>
        /// Submission status'una göre izin verilen feedback türlerini getir
        /// </summary>
        public async Task<List<string>> GetAllowedFeedbackTypesAsync(string submissionStatus)
        {
            var allowedTypes = new List<string>();

            switch (submissionStatus)
            {
                case StatusSubmitted:
                case StatusUnderReview:
                    allowedTypes.AddRange(new[] { FeedbackTypeApproval, FeedbackTypeRejection, FeedbackTypeRevisionRequest });
                    break;
                case StatusRequiresRevision:
                    // Revision durumundayken sadece yeni revision request verilebilir
                    allowedTypes.Add(FeedbackTypeRevisionRequest);
                    break;
                default:
                    // Diğer durumlarda feedback verilemez
                    break;
            }

            return await Task.FromResult(allowedTypes);
        }

        /// <summary>
        /// Pending revision requests listesi (controller için)
        /// </summary>
        public async Task<PagedListDto<FeedbackEntryDto>> GetPendingRevisionRequestsAsync(string controllerId, PagedListCo<GetFeedbackFilterCo> co)
        {
            try
            {
                _logger.LogInformation("Getting pending revision requests for controller: {ControllerId}", controllerId);

                // Controller'ın verdiği revision request'leri getir
                var pendingRequests = await _feedbackStore.GetPendingRevisionRequestsByControllerAsync(controllerId, co);

                _logger.LogInformation("Retrieved {Count} pending revision requests for controller: {ControllerId}",
                    pendingRequests.Count, controllerId);

                return pendingRequests;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending revision requests for controller: {ControllerId}", controllerId);
                throw;
            }
        }

        /// <summary>
        /// Deadline yaklaşan revision requests
        /// </summary>
        public async Task<List<FeedbackEntryDto>> GetUpcomingRevisionDeadlinesAsync(string controllerId, int daysThreshold)
        {
            try
            {
                _logger.LogInformation("Getting upcoming revision deadlines for controller: {ControllerId}, threshold: {Days} days",
                    controllerId, daysThreshold);

                var upcomingDeadlines = await _feedbackStore.GetUpcomingRevisionDeadlinesAsync(controllerId, daysThreshold);

                _logger.LogInformation("Retrieved {Count} upcoming revision deadlines for controller: {ControllerId}",
                    upcomingDeadlines.Count, controllerId);

                return upcomingDeadlines;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting upcoming revision deadlines for controller: {ControllerId}", controllerId);
                throw;
            }
        }

        #endregion
    }
}
