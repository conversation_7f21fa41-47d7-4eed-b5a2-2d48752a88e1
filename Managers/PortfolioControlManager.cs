using AcademicPerformance.Managers.Interfaces;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Services.Interfaces;
using AcademicPerformance.Stores.Interfaces;
using Mapster;
using Microsoft.Extensions.Logging;
using Rlx.Shared.Models.Dtos;
using System.Text.RegularExpressions;

namespace AcademicPerformance.Managers
{
    /// <summary>
    /// Portfolio kontrol manager implementasyonu
    /// Business logic, validation ve ArelBridge entegrasyonu
    /// Portfolio kontrol modülü için kullanılır
    /// </summary>
    public class PortfolioControlManager : IPortfolioControlManager
    {
        private readonly IPortfolioControlStore _portfolioControlStore;
        private readonly IArelBridgeApiService _arelBridgeApiService;
        private readonly ILogger<PortfolioControlManager> _logger;

        public PortfolioControlManager(
            IPortfolioControlStore portfolioControlStore,
            IArelBridgeApiService arelBridgeApiService,
            ILogger<PortfolioControlManager> logger)
        {
            _portfolioControlStore = portfolioControlStore;
            _arelBridgeApiService = arelBridgeApiService;
            _logger = logger;
        }

        #region Course Portfolio Verification Operations

        /// <summary>
        /// Yeni ders portfolio verification kaydı oluşturur
        /// </summary>
        public async Task<CourseVerificationDto> CreateCourseVerificationAsync(CreateCourseVerificationDto createDto, string userId)
        {
            try
            {
                _logger.LogInformation("Creating course verification for academician: {AcademicianTc}, course: {CourseCode}",
                    createDto.AcademicianTc, createDto.CourseCode);

                // Validate input
                var validation = await ValidateVerificationCreateAsync(createDto);
                if (!validation.IsValid)
                {
                    throw new ArgumentException(validation.ErrorMessage);
                }

                // Check if verification already exists
                var existing = await _portfolioControlStore.GetCourseVerificationByKeyAsync(
                    createDto.AcademicianTc, createDto.CourseCode, createDto.PeriodName);

                if (existing != null)
                {
                    throw new InvalidOperationException("Bu ders için verification kaydı zaten mevcut");
                }

                // Create entity
                var entity = new CoursePortfolioVerificationEntity
                {
                    Id = Guid.NewGuid().ToString(),
                    AcademicianTc = createDto.AcademicianTc,
                    CourseCode = createDto.CourseCode,
                    PeriodName = createDto.PeriodName,
                    CourseName = createDto.CourseName,
                    CreatedByUserId = userId
                };

                var createdEntity = await _portfolioControlStore.CreateCourseVerificationAsync(entity);
                var dto = MapEntityToDto(createdEntity);

                _logger.LogInformation("Successfully created course verification with ID: {Id}", dto.Id);
                return dto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating course verification for academician: {AcademicianTc}, course: {CourseCode}",
                    createDto.AcademicianTc, createDto.CourseCode);
                throw;
            }
        }

        /// <summary>
        /// Ders portfolio verification kaydını günceller
        /// </summary>
        public async Task<CourseVerificationDto> UpdateCourseVerificationAsync(CourseVerificationUpdateDto updateDto, string userId)
        {
            try
            {
                _logger.LogInformation("Updating course verification with ID: {Id}", updateDto.Id);

                // Validate input
                var validation = await ValidateVerificationUpdateAsync(updateDto);
                if (!validation.IsValid)
                {
                    throw new ArgumentException(validation.ErrorMessage);
                }

                // Get existing entity
                var entity = await _portfolioControlStore.GetCourseVerificationByIdAsync(updateDto.Id);
                if (entity == null)
                {
                    throw new InvalidOperationException("Verification kaydı bulunamadı");
                }

                // Update fields
                if (!string.IsNullOrEmpty(updateDto.ExamPapersStatus))
                    entity.ExamPapersStatus = updateDto.ExamPapersStatus;
                if (!string.IsNullOrEmpty(updateDto.AnswerKeyStatus))
                    entity.AnswerKeyStatus = updateDto.AnswerKeyStatus;
                if (!string.IsNullOrEmpty(updateDto.ExamRecordStatus))
                    entity.ExamRecordStatus = updateDto.ExamRecordStatus;
                if (!string.IsNullOrEmpty(updateDto.AttendanceSheetStatus))
                    entity.AttendanceSheetStatus = updateDto.AttendanceSheetStatus;
                if (!string.IsNullOrEmpty(updateDto.CourseSyllabusStatus))
                    entity.CourseSyllabusStatus = updateDto.CourseSyllabusStatus;
                if (!string.IsNullOrEmpty(updateDto.WeeklyAttendanceStatus))
                    entity.WeeklyAttendanceStatus = updateDto.WeeklyAttendanceStatus;
                if (!string.IsNullOrEmpty(updateDto.MakeupExamGradesStatus))
                    entity.MakeupExamGradesStatus = updateDto.MakeupExamGradesStatus;
                if (!string.IsNullOrEmpty(updateDto.UzemRecordsStatus))
                    entity.UzemRecordsStatus = updateDto.UzemRecordsStatus;

                if (updateDto.VerificationNotes != null)
                    entity.VerificationNotes = updateDto.VerificationNotes;
                if (updateDto.EbysReferenceId != null)
                    entity.EbysReferenceId = updateDto.EbysReferenceId;

                entity.LastVerifiedAt = DateTime.UtcNow;
                entity.LastVerifiedByArchivistId = userId;
                entity.UpdatedByUserId = userId;

                var updatedEntity = await _portfolioControlStore.UpdateCourseVerificationAsync(entity);
                var dto = MapEntityToDto(updatedEntity);

                _logger.LogInformation("Successfully updated course verification with ID: {Id}", dto.Id);
                return dto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating course verification with ID: {Id}", updateDto.Id);
                throw;
            }
        }

        /// <summary>
        /// ID'ye göre ders portfolio verification kaydını getirir
        /// </summary>
        public async Task<CourseVerificationDto?> GetCourseVerificationByIdAsync(string id)
        {
            try
            {
                _logger.LogInformation("Getting course verification by ID: {Id}", id);

                var entity = await _portfolioControlStore.GetCourseVerificationByIdAsync(id);
                if (entity == null)
                {
                    _logger.LogWarning("Course verification not found with ID: {Id}", id);
                    return null;
                }

                return MapEntityToDto(entity);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting course verification by ID: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// Akademisyen TC'sine göre ders verification kayıtlarını getirir
        /// </summary>
        public async Task<IEnumerable<CourseVerificationDto>> GetCourseVerificationsByAcademicianTcAsync(
            string academicianTc, string? period = null)
        {
            try
            {
                _logger.LogInformation("Getting course verifications for academician: {AcademicianTc}, period: {Period}",
                    academicianTc, period);

                var validation = await ValidateAcademicianTcAsync(academicianTc);
                if (!validation.IsValid)
                {
                    throw new ArgumentException(validation.ErrorMessage);
                }

                var entities = await _portfolioControlStore.GetCourseVerificationsByAcademicianTcAsync(academicianTc, period);
                var dtos = entities.Select(MapEntityToDto);

                _logger.LogInformation("Found {Count} course verifications for academician: {AcademicianTc}",
                    dtos.Count(), academicianTc);

                return dtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting course verifications for academician: {AcademicianTc}, period: {Period}",
                    academicianTc, period);
                throw;
            }
        }

        /// <summary>
        /// Akademisyen için ders verification kayıtlarını getirir (Controller için)
        /// </summary>
        public async Task<IEnumerable<CourseVerificationDto>> GetAcademicianCourseVerificationsAsync(string academicianTc)
        {
            return await GetCourseVerificationsByAcademicianTcAsync(academicianTc);
        }

        /// <summary>
        /// Belirli dönemdeki tüm ders verification kayıtlarını getirir
        /// </summary>
        public async Task<IEnumerable<CourseVerificationDto>> GetCourseVerificationsByPeriodAsync(string period)
        {
            try
            {
                _logger.LogInformation("Getting course verifications for period: {Period}", period);

                var entities = await _portfolioControlStore.GetCourseVerificationsByPeriodAsync(period);
                var dtos = entities.Select(MapEntityToDto);

                _logger.LogInformation("Found {Count} course verifications for period: {Period}",
                    dtos.Count(), period);

                return dtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting course verifications for period: {Period}", period);
                throw;
            }
        }

        /// <summary>
        /// Bekleyen verification kayıtlarını getirir
        /// </summary>
        public async Task<IEnumerable<CourseVerificationDto>> GetPendingCourseVerificationsAsync(
            string? archivistId = null, string? period = null)
        {
            try
            {
                _logger.LogInformation("Getting pending course verifications for archivist: {ArchivistId}, period: {Period}",
                    archivistId, period);

                var entities = await _portfolioControlStore.GetPendingCourseVerificationsAsync(archivistId, period);
                var dtos = entities.Select(MapEntityToDto);

                _logger.LogInformation("Found {Count} pending course verifications", dtos.Count());

                return dtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending course verifications for archivist: {ArchivistId}, period: {Period}",
                    archivistId, period);
                throw;
            }
        }

        /// <summary>
        /// Filtrelenmiş ders verification kayıtları arama
        /// </summary>
        public async Task<PagedListDto<CourseVerificationDto>> SearchCourseVerificationsAsync(
            SearchCourseVerificationRequestDto request)
        {
            try
            {
                _logger.LogInformation("Searching course verifications with filters: {@Request}", request);

                var result = await _portfolioControlStore.SearchCourseVerificationsAsync(request);
                var dtos = result.Data?.Select(MapEntityToDto).ToList() ?? new List<CourseVerificationDto>();

                var pagedResult = new PagedListDto<CourseVerificationDto>
                {
                    Data = dtos,
                    Count = result.Count,
                    TotalCount = result.TotalCount,
                    Page = result.Page,
                    Size = result.Size
                };

                _logger.LogInformation("Found {ItemCount} course verifications out of {TotalCount} total",
                    dtos.Count, result.Count);

                return pagedResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching course verifications with filters: {@Request}", request);
                throw;
            }
        }

        /// <summary>
        /// Toplu verification güncelleme
        /// </summary>
        public async Task<BulkUpdateResultDto> BulkUpdateCourseVerificationsAsync(BulkCourseVerificationUpdateDto bulkUpdateDto, string userId)
        {
            try
            {
                _logger.LogInformation("Bulk updating {Count} course verifications", bulkUpdateDto.Updates.Count);

                var entities = new List<CoursePortfolioVerificationEntity>();

                foreach (var update in bulkUpdateDto.Updates)
                {
                    var entity = await _portfolioControlStore.GetCourseVerificationByIdAsync(update.Id);
                    if (entity == null)
                    {
                        _logger.LogWarning("Course verification not found for bulk update with ID: {Id}", update.Id);
                        continue;
                    }

                    // Apply updates
                    if (!string.IsNullOrEmpty(update.ExamPapersStatus))
                        entity.ExamPapersStatus = update.ExamPapersStatus;
                    if (!string.IsNullOrEmpty(update.AnswerKeyStatus))
                        entity.AnswerKeyStatus = update.AnswerKeyStatus;
                    if (!string.IsNullOrEmpty(update.ExamRecordStatus))
                        entity.ExamRecordStatus = update.ExamRecordStatus;
                    if (!string.IsNullOrEmpty(update.AttendanceSheetStatus))
                        entity.AttendanceSheetStatus = update.AttendanceSheetStatus;
                    if (!string.IsNullOrEmpty(update.CourseSyllabusStatus))
                        entity.CourseSyllabusStatus = update.CourseSyllabusStatus;
                    if (!string.IsNullOrEmpty(update.WeeklyAttendanceStatus))
                        entity.WeeklyAttendanceStatus = update.WeeklyAttendanceStatus;
                    if (!string.IsNullOrEmpty(update.MakeupExamGradesStatus))
                        entity.MakeupExamGradesStatus = update.MakeupExamGradesStatus;
                    if (!string.IsNullOrEmpty(update.UzemRecordsStatus))
                        entity.UzemRecordsStatus = update.UzemRecordsStatus;

                    if (update.VerificationNotes != null)
                        entity.VerificationNotes = update.VerificationNotes;
                    if (update.EbysReferenceId != null)
                        entity.EbysReferenceId = update.EbysReferenceId;

                    entity.LastVerifiedAt = DateTime.UtcNow;
                    entity.LastVerifiedByArchivistId = userId;
                    entity.UpdatedByUserId = userId;

                    entities.Add(entity);
                }

                var updateCount = await _portfolioControlStore.BulkUpdateCourseVerificationsAsync(entities);

                _logger.LogInformation("Successfully bulk updated {Count} course verifications", updateCount);

                return new BulkUpdateResultDto
                {
                    SuccessCount = updateCount,
                    FailedCount = 0,
                    TotalCount = bulkUpdateDto.Updates.Count
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk updating course verifications");
                throw;
            }
        }

        /// <summary>
        /// Verification kaydını siler
        /// </summary>
        public async Task<bool> DeleteCourseVerificationAsync(string id, string userId)
        {
            try
            {
                _logger.LogInformation("Deleting course verification with ID: {Id}", id);

                var result = await _portfolioControlStore.DeleteCourseVerificationAsync(id);

                if (result)
                {
                    _logger.LogInformation("Successfully deleted course verification with ID: {Id}", id);
                }
                else
                {
                    _logger.LogWarning("Course verification not found for deletion with ID: {Id}", id);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting course verification with ID: {Id}", id);
                throw;
            }
        }

        #endregion

        #region ArelBridge Integration

        /// <summary>
        /// ArelBridge'den ders bilgilerini çekerek verification kayıtlarını senkronize eder
        /// </summary>
        public async Task<SyncResultDto> SyncCourseVerificationsFromArelBridgeAsync(string? period = null)
        {
            try
            {
                _logger.LogInformation("Starting sync from ArelBridge for period: {Period}", period);

                // Test ArelBridge connection
                var connectionTest = await _arelBridgeApiService.TestConnectionAsync();
                if (!connectionTest.IsConnected)
                {
                    return new SyncResultDto
                    {
                        IsSuccessful = false,
                        ErrorMessages = { $"ArelBridge connection failed: {connectionTest.Message}" }
                    };
                }

                // Get course information from ArelBridge
                IEnumerable<CourseInformationDto> courseInformations;
                if (!string.IsNullOrEmpty(period))
                {
                    courseInformations = await _arelBridgeApiService.GetCoursesByPeriodAsync(period) ?? new List<CourseInformationDto>();
                }
                else
                {
                    // Get active periods and fetch all courses
                    var periods = await _arelBridgeApiService.GetActivePeriodsAsync() ?? new List<string>();
                    var allCourses = new List<CourseInformationDto>();

                    foreach (var activePeriod in periods)
                    {
                        var periodCourses = await _arelBridgeApiService.GetCoursesByPeriodAsync(activePeriod);
                        if (periodCourses != null)
                        {
                            allCourses.AddRange(periodCourses);
                        }
                    }

                    courseInformations = allCourses;
                }

                // Sync with portfolio control store
                var syncResult = await _portfolioControlStore.SyncCourseVerificationsAsync(courseInformations);

                _logger.LogInformation("Sync completed. Created: {Created}, Updated: {Updated}, Skipped: {Skipped}, Errors: {Errors}",
                    syncResult.CreatedCount, syncResult.UpdatedCount, syncResult.SkippedCount, syncResult.ErrorMessages.Count);

                return syncResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing course verifications from ArelBridge for period: {Period}", period);
                return new SyncResultDto
                {
                    IsSuccessful = false,
                    ErrorMessages = { $"Sync failed: {ex.Message}" }
                };
            }
        }

        /// <summary>
        /// Belirli akademisyen için ArelBridge'den ders bilgilerini çekerek senkronize eder
        /// </summary>
        public async Task<SyncResultDto> SyncAcademicianCourseVerificationsAsync(string academicianTc)
        {
            try
            {
                _logger.LogInformation("Starting sync for academician: {AcademicianTc} from ArelBridge", academicianTc);

                var validation = await ValidateAcademicianTcAsync(academicianTc);
                if (!validation.IsValid)
                {
                    return new SyncResultDto
                    {
                        IsSuccessful = false,
                        ErrorMessages = { validation.ErrorMessage }
                    };
                }

                // Test ArelBridge connection
                var connectionTest = await _arelBridgeApiService.TestConnectionAsync();
                if (!connectionTest.IsConnected)
                {
                    return new SyncResultDto
                    {
                        IsSuccessful = false,
                        ErrorMessages = { $"ArelBridge connection failed: {connectionTest.Message}" }
                    };
                }

                // Get course information for academician
                var courseInformations = await _arelBridgeApiService.GetCoursesByAcademicianTcAsync(academicianTc);
                if (courseInformations == null)
                {
                    return new SyncResultDto
                    {
                        IsSuccessful = false,
                        ErrorMessages = { "No course information found for academician" }
                    };
                }

                // Sync with portfolio control store
                var syncResult = await _portfolioControlStore.SyncAcademicianCourseVerificationsAsync(academicianTc, courseInformations);

                _logger.LogInformation("Academician sync completed. Created: {Created}, Updated: {Updated}, Skipped: {Skipped}, Errors: {Errors}",
                    syncResult.CreatedCount, syncResult.UpdatedCount, syncResult.SkippedCount, syncResult.ErrorMessages.Count);

                return syncResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing courses for academician: {AcademicianTc} from ArelBridge", academicianTc);
                return new SyncResultDto
                {
                    IsSuccessful = false,
                    ErrorMessages = { $"Academician sync failed: {ex.Message}" }
                };
            }
        }

        /// <summary>
        /// ArelBridge bağlantısını test eder
        /// </summary>
        public async Task<(bool IsConnected, string Message)> TestArelBridgeConnectionAsync()
        {
            try
            {
                return await _arelBridgeApiService.TestConnectionAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing ArelBridge connection");
                return (false, $"Connection test failed: {ex.Message}");
            }
        }

        #endregion

        #region Dashboard Operations

        /// <summary>
        /// Archivist dashboard verilerini getirir
        /// </summary>
        public async Task<ArchivistDashboardResponseDto> GetArchivistDashboardAsync(string archivistId, string? period = null)
        {
            try
            {
                _logger.LogInformation("Getting archivist dashboard for: {ArchivistId}, period: {Period}", archivistId, period);

                var dashboard = new ArchivistDashboardResponseDto();

                // Get pending verifications
                var pendingVerifications = await GetPendingCourseVerificationsAsync(archivistId, period);
                dashboard.PendingVerifications = pendingVerifications.Take(10).ToList(); // Limit to 10 for dashboard

                // Get recently updated verifications
                var searchRequest = new SearchCourseVerificationRequestDto
                {
                    ArchivistId = archivistId,
                    Period = period,
                    SortBy = "UpdatedAt",
                    SortDirection = "desc",
                    PageSize = 10
                };
                var recentlyUpdated = await SearchCourseVerificationsAsync(searchRequest);
                dashboard.RecentlyUpdated = recentlyUpdated.Data?.ToList() ?? new List<CourseVerificationDto>();

                // Get dashboard statistics
                dashboard.Statistics = await GetDashboardStatisticsAsync(period);

                // Get archivist statistics
                dashboard.ArchivistStatistics = await GetArchivistVerificationStatisticsAsync(archivistId, period);

                return dashboard;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting archivist dashboard for: {ArchivistId}, period: {Period}", archivistId, period);
                throw;
            }
        }

        /// <summary>
        /// Genel verification dashboard istatistiklerini getirir
        /// </summary>
        public async Task<VerificationDashboardStatisticsDto> GetDashboardStatisticsAsync(string? period = null)
        {
            try
            {
                return await _portfolioControlStore.GetDashboardStatisticsAsync(period);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard statistics for period: {Period}", period);
                throw;
            }
        }

        /// <summary>
        /// Akademisyen bazlı verification istatistiklerini getirir
        /// </summary>
        public async Task<CourseVerificationStatisticsDto> GetAcademicianVerificationStatisticsAsync(
            string academicianTc, string? period = null)
        {
            try
            {
                var validation = await ValidateAcademicianTcAsync(academicianTc);
                if (!validation.IsValid)
                {
                    throw new ArgumentException(validation.ErrorMessage);
                }

                return await _portfolioControlStore.GetAcademicianVerificationStatisticsAsync(academicianTc, period);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting academician verification statistics for: {AcademicianTc}, period: {Period}",
                    academicianTc, period);
                throw;
            }
        }

        /// <summary>
        /// Dönem bazlı verification istatistiklerini getirir
        /// </summary>
        public async Task<CourseVerificationStatisticsDto> GetPeriodVerificationStatisticsAsync(string period)
        {
            try
            {
                return await _portfolioControlStore.GetPeriodVerificationStatisticsAsync(period);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting period verification statistics for: {Period}", period);
                throw;
            }
        }

        /// <summary>
        /// Archivist bazlı verification istatistiklerini getirir
        /// </summary>
        public async Task<CourseVerificationStatisticsDto> GetArchivistVerificationStatisticsAsync(
            string archivistId, string? period = null)
        {
            try
            {
                return await _portfolioControlStore.GetArchivistVerificationStatisticsAsync(archivistId, period);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting archivist verification statistics for: {ArchivistId}, period: {Period}",
                    archivistId, period);
                throw;
            }
        }

        #endregion

        #region Validation

        /// <summary>
        /// Verification güncelleme DTO'sunu validate eder
        /// </summary>
        public async Task<(bool IsValid, string ErrorMessage)> ValidateVerificationUpdateAsync(CourseVerificationUpdateDto updateDto)
        {
            if (updateDto == null)
                return (false, "Update DTO boş olamaz");

            if (string.IsNullOrEmpty(updateDto.Id))
                return (false, "Verification ID boş olamaz");

            // Validate status values
            var statusFields = new[]
            {
                updateDto.ExamPapersStatus, updateDto.AnswerKeyStatus, updateDto.ExamRecordStatus,
                updateDto.AttendanceSheetStatus, updateDto.CourseSyllabusStatus, updateDto.WeeklyAttendanceStatus,
                updateDto.MakeupExamGradesStatus, updateDto.UzemRecordsStatus
            };

            foreach (var status in statusFields.Where(s => !string.IsNullOrEmpty(s)))
            {
                var statusValidation = ValidateVerificationStatus(status!);
                if (!statusValidation.IsValid)
                    return statusValidation;
            }

            return (true, string.Empty);
        }

        /// <summary>
        /// Verification oluşturma DTO'sunu validate eder
        /// </summary>
        public async Task<(bool IsValid, string ErrorMessage)> ValidateVerificationCreateAsync(CreateCourseVerificationDto createDto)
        {
            if (createDto == null)
                return (false, "Create DTO boş olamaz");

            var tcValidation = await ValidateAcademicianTcAsync(createDto.AcademicianTc);
            if (!tcValidation.IsValid)
                return tcValidation;

            if (string.IsNullOrEmpty(createDto.CourseCode))
                return (false, "Ders kodu boş olamaz");

            if (string.IsNullOrEmpty(createDto.PeriodName))
                return (false, "Dönem adı boş olamaz");

            if (string.IsNullOrEmpty(createDto.CourseName))
                return (false, "Ders adı boş olamaz");

            return (true, string.Empty);
        }

        /// <summary>
        /// Akademisyen TC kimlik numarasını validate eder
        /// </summary>
        public async Task<(bool IsValid, string ErrorMessage)> ValidateAcademicianTcAsync(string academicianTc)
        {
            if (string.IsNullOrWhiteSpace(academicianTc))
                return (false, "Akademisyen TC kimlik numarası boş olamaz");

            if (academicianTc.Length != 11)
                return (false, "Akademisyen TC kimlik numarası 11 haneli olmalıdır");

            if (!academicianTc.All(char.IsDigit))
                return (false, "Akademisyen TC kimlik numarası sadece rakamlardan oluşmalıdır");

            // TC kimlik numarası algoritması kontrolü
            if (!IsValidTcNumber(academicianTc))
                return (false, "Geçersiz TC kimlik numarası formatı");

            return (true, string.Empty);
        }

        /// <summary>
        /// Verification status değerini validate eder
        /// </summary>
        public (bool IsValid, string ErrorMessage) ValidateVerificationStatus(string status)
        {
            if (string.IsNullOrEmpty(status))
                return (false, "Status değeri boş olamaz");

            if (!VerificationStatus.IsValid(status))
                return (false, $"Geçersiz status değeri: {status}. Geçerli değerler: {string.Join(", ", VerificationStatus.AllStatuses)}");

            return (true, string.Empty);
        }

        #endregion

        #region Health Check

        /// <summary>
        /// Manager sağlık kontrolü yapar
        /// </summary>
        public async Task<(bool IsHealthy, string Message, Dictionary<string, object> Details)> HealthCheckAsync()
        {
            var details = new Dictionary<string, object>();

            try
            {
                // Test store health
                var storeHealth = await _portfolioControlStore.HealthCheckAsync();
                details["StoreHealth"] = new { storeHealth.IsHealthy, storeHealth.Message };

                if (!storeHealth.IsHealthy)
                {
                    return (false, "Portfolio Control Store is unhealthy", details);
                }

                // Test ArelBridge connection
                var arelBridgeHealth = await _arelBridgeApiService.TestConnectionAsync();
                details["ArelBridgeHealth"] = new { arelBridgeHealth.IsConnected, arelBridgeHealth.Message };

                if (!arelBridgeHealth.IsConnected)
                {
                    return (false, "ArelBridge connection is unhealthy", details);
                }

                return (true, "Portfolio Control Manager is healthy", details);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Portfolio Control Manager health check failed");
                details["Error"] = ex.Message;
                return (false, "Portfolio Control Manager health check failed", details);
            }
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Entity'yi DTO'ya map eder
        /// </summary>
        private CourseVerificationDto MapEntityToDto(CoursePortfolioVerificationEntity entity)
        {
            var dto = entity.Adapt<CourseVerificationDto>();
            dto.OverallStatus = entity.GetOverallStatus();
            dto.CompletionPercentage = entity.GetCompletionPercentage();
            dto.PendingCount = entity.GetPendingCount();
            dto.IssueCount = entity.GetIssueCount();
            return dto;
        }

        /// <summary>
        /// TC kimlik numarası algoritması kontrolü
        /// </summary>
        private static bool IsValidTcNumber(string tcNumber)
        {
            if (tcNumber.Length != 11 || !tcNumber.All(char.IsDigit))
                return false;

            var digits = tcNumber.Select(c => int.Parse(c.ToString())).ToArray();

            // İlk rakam 0 olamaz
            if (digits[0] == 0)
                return false;

            // 10. rakam kontrolü
            var oddSum = digits[0] + digits[2] + digits[4] + digits[6] + digits[8];
            var evenSum = digits[1] + digits[3] + digits[5] + digits[7];
            var tenthDigit = ((oddSum * 7) - evenSum) % 10;

            if (digits[9] != tenthDigit)
                return false;

            // 11. rakam kontrolü
            var totalSum = digits.Take(10).Sum();
            var eleventhDigit = totalSum % 10;

            return digits[10] == eleventhDigit;
        }

        #endregion
    }
}
