using AcademicPerformance.DbContexts;
using AcademicPerformance.Extensions;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;

namespace AcademicPerformance.Stores
{
    /// <summary>
    /// Personel yetkinlik değerlendirme veri katmanı
    /// </summary>
    public class StaffCompetencyStore : IStaffCompetencyStore
    {
        private readonly AcademicPerformanceDbContext _context;
        private readonly ILogger<StaffCompetencyStore> _logger;
        private readonly IPerformanceMonitoringService _performanceMonitoringService;

        public StaffCompetencyStore(
            AcademicPerformanceDbContext context,
            ILogger<StaffCompetencyStore> logger,
            IPerformanceMonitoringService performanceMonitoringService)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _performanceMonitoringService = performanceMonitoringService ?? throw new ArgumentNullException(nameof(performanceMonitoringService));
        }

        #region CRUD Operations

        public async Task<StaffCompetencyEvaluationEntity> CreateStaffCompetencyAsync(StaffCompetencyEvaluationEntity entity)
        {
            try
            {
                _context.StaffCompetencyEvaluations.Add(entity);
                await _context.SaveChangesAsync();
                return entity;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik değerlendirmesi oluşturulurken hata - Staff: {StaffId}", entity.AcademicianUniveristyUserId);
                throw;
            }
        }

        public async Task<bool> UpdateStaffCompetencyAsync(StaffCompetencyEvaluationEntity entity)
        {
            return await UpdateStaffCompetencyAsync(entity, "system");
        }

        public async Task<bool> UpdateStaffCompetencyAsync(StaffCompetencyEvaluationEntity entity, string updatedByUserId)
        {
            try
            {
                entity.Disabled = false;
                var result = await _context.SaveChangesAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik değerlendirmesi güncellenirken hata - ID: {Id}", entity.Id);
                return false;
            }
        }

        public async Task<bool> DeleteStaffCompetencyAsync(string id, string deletedByUserId)
        {
            try
            {
                var entity = await _context.StaffCompetencyEvaluations
                    .FirstOrDefaultAsync(e => e.Id == id && !e.Deleted);

                if (entity == null)
                    return false;

                entity.Disabled = false;
                var result = await _context.SaveChangesAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik değerlendirmesi silinirken hata - ID: {Id}", id);
                return false;
            }
        }

        public async Task<StaffCompetencyEvaluationEntity?> GetStaffCompetencyAsync(string id)
        {
            try
            {
                return await _context.StaffCompetencyEvaluations
                    .Include(e => e.CompetencyRatings)
                    .FirstOrDefaultAsync(e => e.Id == id && !e.Disabled);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik değerlendirmesi getirilirken hata - ID: {Id}", id);
                throw;
            }
        }

        public async Task<PagedListDto<StaffCompetencyEvaluationEntity>> GetStaffCompetenciesAsync(
            PagedListCo<StaffCompetencyFilterDto> co)
        {
            try
            {
                var query = _context.StaffCompetencyEvaluations
                    .Include(e => e.CompetencyRatings)
                    .Where(e => !e.Disabled);

                // Apply basic filters
                if (co.Criteria != null)
                {
                    if (!string.IsNullOrEmpty(co.Criteria!.StaffId))
                        query = query.Where(e => e.AcademicianUniveristyUserId == co.Criteria!.StaffId);

                    if (!string.IsNullOrEmpty(co.Criteria!.EvaluatorId))
                        query = query.Where(e => e.EvaluatingManagerUserId == co.Criteria!.EvaluatorId);

                    if (!string.IsNullOrEmpty(co.Criteria!.EvaluationPeriod))
                        query = query.Where(e => e.EvaluationContextId == co.Criteria!.EvaluationPeriod);

                    if (co.Criteria!.EvaluationDateFrom.HasValue)
                        query = query.Where(e => e.SubmittedAt >= co.Criteria!.EvaluationDateFrom.Value);

                    if (co.Criteria!.EvaluationDateTo.HasValue)
                        query = query.Where(e => e.SubmittedAt <= co.Criteria!.EvaluationDateTo.Value);
                }

                var totalCount = await query.CountAsync();

                var items = await query
                    .OrderByDescending(e => e.SubmittedAt)
                    .Skip((co.Pager.Page - 1) * co.Pager.Size)
                    .Take(co.Pager.Size)
                    .ToListAsync();

                return new PagedListDto<StaffCompetencyEvaluationEntity>
                {
                    Data = items,
                    TotalCount = totalCount,
                    Page = co.Pager.Page,
                    Size = co.Pager.Size,
                    Count = items.Count
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik değerlendirmeleri listelenirken hata");
                throw;
            }
        }

        #endregion

        #region Query Operations

        public async Task<bool> HasExistingEvaluationAsync(string staffId, string period)
        {
            try
            {
                return await _context.StaffCompetencyEvaluations
                    .AnyAsync(e => e.AcademicianUniveristyUserId == staffId
                                && e.EvaluationContextId == period
                                && !e.Disabled);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Mevcut değerlendirme kontrolü yapılırken hata - Staff: {StaffId}", staffId);
                throw;
            }
        }

        public async Task<List<StaffCompetencyEvaluationEntity>> GetStaffCompetenciesByDepartmentAsync(string departmentId, string period)
        {
            try
            {
                return await _context.StaffCompetencyEvaluations
                    .Include(e => e.CompetencyRatings)
                    .Where(e => e.EvaluationContextId == period && !e.Disabled)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm yetkinlik değerlendirmeleri getirilirken hata - Department: {DepartmentId}", departmentId);
                throw;
            }
        }

        public async Task<double> CalculateCompetencyAreaScoreAsync(string staffId, string competencyArea, string period)
        {
            try
            {
                var latestEvaluation = await _context.StaffCompetencyEvaluations
                    .Include(e => e.CompetencyRatings)
                    .Where(e => e.AcademicianUniveristyUserId == staffId
                             && e.EvaluationContextId == period
                             && !e.Disabled)
                    .OrderByDescending(e => e.SubmittedAt)
                    .FirstOrDefaultAsync();

                if (latestEvaluation?.CompetencyRatings == null)
                    return 0;

                // Belirli yetkinlik alanındaki rating'leri filtrele
                var areaRatings = latestEvaluation.CompetencyRatings
                    .Where(r => r.CompetencySystemId?.Contains(competencyArea, StringComparison.OrdinalIgnoreCase) == true)
                    .ToList();

                if (!areaRatings.Any())
                    return 0;

                // Rating'leri sayısal değerlere çevir ve ortalama al
                var numericRatings = areaRatings
                    .Select(r => ConvertRatingToNumeric(r.Rating))
                    .Where(rating => rating > 0)
                    .ToList();

                return numericRatings.Any() ? numericRatings.Average() : 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Yetkinlik alanı skoru hesaplanırken hata - Staff: {StaffId}, Area: {Area}", staffId, competencyArea);
                throw;
            }
        }

        #endregion

        #region Helper Methods

        private double ConvertRatingToNumeric(string rating)
        {
            return rating?.ToLower() switch
            {
                "excellent" or "5" => 5.0,
                "good" or "4" => 4.0,
                "average" or "3" => 3.0,
                "below_average" or "2" => 2.0,
                "poor" or "1" => 1.0,
                _ => double.TryParse(rating, out double value) ? value : 0.0
            };
        }

        /// <summary>
        /// Standart sapma hesapla
        /// </summary>
        private double CalculateStandardDeviation(IEnumerable<double> values)
        {
            var valuesList = values.ToList();
            if (valuesList.Count <= 1) return 0;

            var average = valuesList.Average();
            var sumOfSquaresOfDifferences = valuesList.Select(val => (val - average) * (val - average)).Sum();
            return Math.Sqrt(sumOfSquaresOfDifferences / valuesList.Count);
        }

        /// <summary>
        /// Ortalama skora göre performans seviyesi belirle
        /// </summary>
        private string DeterminePerformanceLevel(double averageScore)
        {
            return averageScore switch
            {
                >= 4.5 => "Excellent",
                >= 3.5 => "Good",
                >= 2.5 => "Average",
                >= 1.5 => "BelowAverage",
                _ => "Poor"
            };
        }

        /// <summary>
        /// Trend yönünü hesapla
        /// </summary>
        private string CalculateTrendDirection(IEnumerable<double> scores)
        {
            var scoresList = scores.ToList();
            if (scoresList.Count < 2) return "Stable";

            var firstHalf = scoresList.Take(scoresList.Count / 2).Average();
            var secondHalf = scoresList.Skip(scoresList.Count / 2).Average();

            var difference = secondHalf - firstHalf;

            return difference switch
            {
                > 0.2 => "Increasing",
                < -0.2 => "Decreasing",
                _ => "Stable"
            };
        }

        /// <summary>
        /// Büyüme oranını hesapla
        /// </summary>
        private double CalculateGrowthRate(IEnumerable<double> scores)
        {
            var scoresList = scores.ToList();
            if (scoresList.Count < 2) return 0;

            var initialScore = scoresList.First();
            var finalScore = scoresList.Last();

            return initialScore == 0 ? 0 : ((finalScore - initialScore) / initialScore) * 100;
        }

        #endregion

        #region Interface Implementation - Placeholder Methods

        // Bu method'lar interface'i implement etmek için gerekli
        // Gerçek implementasyon daha sonra yapılacak

        public async Task<List<CompetencyAreaSummaryDto>> CalculateDepartmentCompetencyStatisticsAsync(string departmentId, string period)
        {
            try
            {
                _logger.LogInformation("Bölüm yetkinlik istatistikleri hesaplanıyor - Department: {DepartmentId}, Period: {Period}",
                    departmentId, period);

                // Belirtilen bölüm ve dönem için tüm değerlendirmeleri getir
                var query = _context.StaffCompetencyEvaluations
                    .Include(e => e.CompetencyRatings)
                    .ThenInclude(r => r.CompetencyDefinition)
                    .Where(e => e.EvaluationContextId == period && !e.Disabled);

                // DepartmentId filtresi ekle (eğer belirtilmişse)
                if (!string.IsNullOrEmpty(departmentId))
                {
                    query = query.Where(e => e.DepartmentId == departmentId);
                }

                var evaluations = await query.ToListAsync();

                if (!evaluations.Any())
                {
                    _logger.LogInformation("Belirtilen dönem için değerlendirme bulunamadı - Department: {DepartmentId}, Period: {Period}",
                        departmentId, period);
                    return new List<CompetencyAreaSummaryDto>();
                }

                // Yetkinlik alanlarına göre grupla ve istatistikleri hesapla
                var competencyStats = evaluations
                    .SelectMany(e => e.CompetencyRatings ?? new List<CompetencyRatingEntity>())
                    .Where(r => r.CompetencyDefinition != null)
                    .GroupBy(r => r.CompetencySystemId)
                    .Select(g => new CompetencyAreaSummaryDto
                    {
                        CompetencySystemId = g.Key,
                        CompetencyName = g.First().CompetencyDefinition?.Name ?? "Bilinmeyen Yetkinlik",
                        TotalEvaluations = g.Count(),
                        AverageScore = g.Average(r => ConvertRatingToNumeric(r.Rating)),
                        MinScore = g.Min(r => ConvertRatingToNumeric(r.Rating)),
                        MaxScore = g.Max(r => ConvertRatingToNumeric(r.Rating)),
                        StandardDeviation = CalculateStandardDeviation(g.Select(r => ConvertRatingToNumeric(r.Rating))),
                        DistributionByRating = g.GroupBy(r => r.Rating)
                            .ToDictionary(rg => rg.Key, rg => rg.Count()),
                        PerformanceLevel = DeterminePerformanceLevel(g.Average(r => ConvertRatingToNumeric(r.Rating)))
                    })
                    .OrderByDescending(s => s.AverageScore)
                    .ToList();

                _logger.LogInformation("Bölüm yetkinlik istatistikleri hesaplandı - {Count} yetkinlik alanı, Department: {DepartmentId}",
                    competencyStats.Count, departmentId);

                return competencyStats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm yetkinlik istatistikleri hesaplanırken hata - Department: {DepartmentId}, Period: {Period}",
                    departmentId, period);
                throw;
            }
        }

        public async Task<List<CompetencyTrendDataDto>> GetCompetencyTrendAnalysisAsync(string staffId, int periodCount)
        {
            try
            {
                _logger.LogInformation("Personel yetkinlik trend analizi yapılıyor - Staff: {StaffId}, PeriodCount: {PeriodCount}",
                    staffId, periodCount);

                // Son N dönemdeki değerlendirmeleri getir
                var evaluations = await _context.StaffCompetencyEvaluations
                    .Include(e => e.CompetencyRatings)
                    .ThenInclude(r => r.CompetencyDefinition)
                    .Where(e => e.AcademicianUniveristyUserId == staffId && !e.Disabled)
                    .OrderByDescending(e => e.SubmittedAt)
                    .Take(periodCount)
                    .ToListAsync();

                if (!evaluations.Any())
                {
                    _logger.LogInformation("Personel için değerlendirme bulunamadı - Staff: {StaffId}", staffId);
                    return new List<CompetencyTrendDataDto>();
                }

                var trendData = new List<CompetencyTrendDataDto>();

                // Yetkinlik alanlarına göre grupla
                var competencyGroups = evaluations
                    .SelectMany(e => e.CompetencyRatings?.Select(r => new
                    {
                        Evaluation = e,
                        Rating = r
                    }) ?? Enumerable.Empty<dynamic>())
                    .Where(x => x.Rating != null)
                    .GroupBy(x => x.Rating.CompetencySystemId);

                foreach (var competencyGroup in competencyGroups)
                {
                    var competencyName = competencyGroup.First().Rating.CompetencyDefinition?.Name ?? "Bilinmeyen Yetkinlik";

                    var periodScores = competencyGroup
                        .GroupBy(x => x.Evaluation.EvaluationContextId)
                        .Select(pg => new CompetencyPeriodScoreDto
                        {
                            Period = pg.Key,
                            Score = pg.Average(x => ConvertRatingToNumeric(x.Rating.Rating)),
                            EvaluationDate = pg.First().Evaluation.SubmittedAt
                        })
                        .OrderBy(ps => ps.EvaluationDate)
                        .ToList();

                    // Trend yönü ve büyüme oranı hesapla
                    var trendDirection = CalculateTrendDirection(periodScores.Select(ps => ps.Score));
                    var growthRate = CalculateGrowthRate(periodScores.Select(ps => ps.Score));

                    trendData.Add(new CompetencyTrendDataDto
                    {
                        CompetencySystemId = competencyGroup.Key,
                        CompetencyName = competencyName,
                        PeriodScores = periodScores,
                        TrendDirection = trendDirection,
                        GrowthRate = growthRate,
                        CurrentScore = periodScores.LastOrDefault()?.Score ?? 0,
                        InitialScore = periodScores.FirstOrDefault()?.Score ?? 0
                    });
                }

                _logger.LogInformation("Personel yetkinlik trend analizi tamamlandı - {Count} yetkinlik alanı, Staff: {StaffId}",
                    trendData.Count, staffId);

                return trendData.OrderByDescending(t => t.GrowthRate).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik trend analizi hatası - Staff: {StaffId}", staffId);
                throw;
            }
        }

        public async Task<StaffCompetencyComparisonDto> CompareStaffCompetenciesAsync(List<string> staffIds, string period)
        {
            try
            {
                _logger.LogInformation("Personel yetkinlik karşılaştırması yapılıyor - Staff Count: {Count}, Period: {Period}",
                    staffIds.Count, period);

                var comparison = new StaffCompetencyComparisonDto
                {
                    StaffIds = staffIds,
                    ComparisonPeriod = period,
                    GeneratedAt = DateTime.UtcNow
                };

                var comparisonData = new List<StaffCompetencyComparisonItemDto>();

                foreach (var staffId in staffIds)
                {
                    var evaluations = await GetStaffCompetenciesByStaffIdAsync(staffId, period, 1);
                    var latestEvaluation = evaluations.FirstOrDefault();

                    if (latestEvaluation?.CompetencyRatings != null)
                    {
                        var competencyScores = latestEvaluation.CompetencyRatings
                            .GroupBy(r => r.CompetencySystemId)
                            .ToDictionary(
                                g => g.Key,
                                g => g.Average(r => ConvertRatingToNumeric(r.Rating))
                            );

                        var overallScore = competencyScores.Values.Any() ? competencyScores.Values.Average() : 0;

                        comparisonData.Add(new StaffCompetencyComparisonItemDto
                        {
                            StaffId = staffId,
                            StaffName = $"Personel {staffId}", // Bu bilgi başka servisten alınacak
                            OverallScore = overallScore,
                            CompetencyScores = competencyScores,
                            EvaluationDate = latestEvaluation.SubmittedAt,
                            Ranking = 0 // Sonra hesaplanacak
                        });
                    }
                }

                // Ranking hesapla
                var rankedData = comparisonData
                    .OrderByDescending(c => c.OverallScore)
                    .Select((item, index) =>
                    {
                        item.Ranking = index + 1;
                        return item;
                    })
                    .ToList();

                comparison.ComparisonData = rankedData;

                // Benchmark hesapla
                if (rankedData.Any())
                {
                    comparison.Benchmark = new CompetencyBenchmarkDto
                    {
                        Period = period,
                        AverageOverallScore = rankedData.Average(c => c.OverallScore),
                        BestScore = rankedData.Max(c => c.OverallScore),
                        WorstScore = rankedData.Min(c => c.OverallScore),
                        TopPerformer = rankedData.First().StaffName,
                        BottomPerformer = rankedData.Last().StaffName,
                        TotalStaffEvaluated = rankedData.Count,
                        StandardDeviation = CalculateStandardDeviation(rankedData.Select(c => c.OverallScore)),
                        BenchmarkDate = DateTime.UtcNow
                    };
                }

                _logger.LogInformation("Personel yetkinlik karşılaştırması tamamlandı - {Count} personel karşılaştırıldı",
                    comparisonData.Count);

                return comparison;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik karşılaştırması hatası - Staff Count: {Count}", staffIds.Count);
                throw;
            }
        }

        public Task<StaffCompetencyAnalysisDto> AnalyzeStaffCompetencyAsync(string analysisType, string targetId, string period, string analyzedByUserId)
        {
            return Task.FromResult(new StaffCompetencyAnalysisDto());
        }

        public async Task<CompetencyBenchmarkDto> CalculateCompetencyBenchmarkAsync(string departmentId, string period)
        {
            try
            {
                _logger.LogInformation("Yetkinlik benchmark hesaplanıyor - Department: {DepartmentId}, Period: {Period}",
                    departmentId, period);

                // Belirtilen bölüm ve dönem için tüm değerlendirmeleri getir
                var query = _context.StaffCompetencyEvaluations
                    .Include(e => e.CompetencyRatings)
                    .ThenInclude(r => r.CompetencyDefinition)
                    .Where(e => e.EvaluationContextId == period && !e.Disabled);

                if (!string.IsNullOrEmpty(departmentId))
                {
                    query = query.Where(e => e.DepartmentId == departmentId);
                }

                var evaluations = await query.ToListAsync();

                if (!evaluations.Any())
                {
                    _logger.LogInformation("Benchmark hesaplama için değerlendirme bulunamadı - Department: {DepartmentId}, Period: {Period}",
                        departmentId, period);
                    return new CompetencyBenchmarkDto
                    {
                        Period = period,
                        BenchmarkDate = DateTime.UtcNow
                    };
                }

                // Tüm skorları topla
                var allScores = evaluations
                    .SelectMany(e => e.CompetencyRatings ?? new List<CompetencyRatingEntity>())
                    .Where(r => r.CompetencyDefinition != null)
                    .Select(r => ConvertRatingToNumeric(r.Rating))
                    .ToList();

                if (!allScores.Any())
                {
                    return new CompetencyBenchmarkDto
                    {
                        Period = period,
                        BenchmarkDate = DateTime.UtcNow
                    };
                }

                // En iyi ve en kötü performansı bul
                var topPerformer = evaluations
                    .Select(e => new
                    {
                        StaffId = e.AcademicianUniveristyUserId,
                        AverageScore = e.CompetencyRatings?.Any() == true
                            ? e.CompetencyRatings.Average(r => ConvertRatingToNumeric(r.Rating))
                            : 0
                    })
                    .OrderByDescending(x => x.AverageScore)
                    .FirstOrDefault();

                var bottomPerformer = evaluations
                    .Select(e => new
                    {
                        StaffId = e.AcademicianUniveristyUserId,
                        AverageScore = e.CompetencyRatings?.Any() == true
                            ? e.CompetencyRatings.Average(r => ConvertRatingToNumeric(r.Rating))
                            : 0
                    })
                    .OrderBy(x => x.AverageScore)
                    .FirstOrDefault();

                var benchmark = new CompetencyBenchmarkDto
                {
                    Period = period,
                    AverageOverallScore = allScores.Average(),
                    BestScore = allScores.Max(),
                    WorstScore = allScores.Min(),
                    TopPerformer = topPerformer?.StaffId ?? "Bilinmiyor",
                    BottomPerformer = bottomPerformer?.StaffId ?? "Bilinmiyor",
                    TotalStaffEvaluated = evaluations.Count,
                    StandardDeviation = CalculateStandardDeviation(allScores),
                    BenchmarkDate = DateTime.UtcNow
                };

                _logger.LogInformation("Yetkinlik benchmark hesaplandı - {Count} personel değerlendirildi, Ortalama: {Average:F2}",
                    benchmark.TotalStaffEvaluated, benchmark.AverageOverallScore);

                return benchmark;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Yetkinlik benchmark hesaplama hatası - Department: {DepartmentId}, Period: {Period}",
                    departmentId, period);
                throw;
            }
        }

        public Task<CompetencyEvaluationFormDto> CreateEvaluationFormAsync(CompetencyEvaluationFormCreateDto dto, string createdByUserId)
        {
            return Task.FromResult(new CompetencyEvaluationFormDto());
        }

        public Task<CompetencyEvaluationFormDto?> GetEvaluationFormAsync(string formId)
        {
            return Task.FromResult<CompetencyEvaluationFormDto?>(null);
        }

        public async Task<List<CompetencyEvaluationFormDto>> GetEvaluationFormsForStaffAsync(string staffId, string period)
        {
            try
            {
                _logger.LogInformation("Personel için değerlendirme formları getiriliyor - Staff: {StaffId}, Period: {Period}",
                    staffId, period);

                // Personel için değerlendirme formlarını getir
                var evaluations = await _context.StaffCompetencyEvaluations
                    .Include(e => e.CompetencyRatings)
                    .ThenInclude(r => r.CompetencyDefinition)
                    .Where(e => e.AcademicianUniveristyUserId == staffId &&
                               e.EvaluationContextId == period &&
                               !e.Disabled)
                    .OrderByDescending(e => e.SubmittedAt)
                    .ToListAsync();

                var forms = evaluations.Select(evaluation => new CompetencyEvaluationFormDto
                {
                    Id = evaluation.Id,
                    StaffId = evaluation.AcademicianUniveristyUserId ?? "",
                    StaffName = $"Personel {evaluation.AcademicianUniveristyUserId}",
                    Period = evaluation.EvaluationContextId ?? "",
                    EvaluatorId = evaluation.EvaluatorUserId ?? "",
                    EvaluatorName = $"Değerlendirici {evaluation.EvaluatorUserId}",
                    Status = evaluation.Status,
                    EvaluationDate = evaluation.SubmittedAt,
                    SubmissionDate = evaluation.SubmittedAt,
                    OverallScore = evaluation.CompetencyRatings?.Any() == true
                        ? evaluation.CompetencyRatings.Average(r => ConvertRatingToNumeric(r.Rating))
                        : 0,
                    Categories = new List<CompetencyEvaluationCategoryDto>() // Basit implementasyon
                }).ToList();

                _logger.LogInformation("Personel için değerlendirme formları getirildi - {Count} form, Staff: {StaffId}",
                    forms.Count, staffId);

                return forms;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel için değerlendirme formları getirme hatası - Staff: {StaffId}", staffId);
                throw;
            }
        }

        public async Task<bool> ApproveEvaluationFormAsync(string formId, string approvedByUserId)
        {
            try
            {
                _logger.LogInformation("Değerlendirme formu onaylanıyor - Form: {FormId}, ApprovedBy: {ApprovedBy}",
                    formId, approvedByUserId);

                var evaluation = await _context.StaffCompetencyEvaluations
                    .FirstOrDefaultAsync(e => e.Id == formId && !e.Disabled);

                if (evaluation == null)
                {
                    _logger.LogWarning("Onaylanacak değerlendirme formu bulunamadı - Form: {FormId}", formId);
                    return false;
                }

                // Durum kontrolü - sadece Submitted durumundaki formlar onaylanabilir
                if (evaluation.Status != "Submitted")
                {
                    _logger.LogWarning("Değerlendirme formu onay için uygun durumda değil - Form: {FormId}, Status: {Status}",
                        formId, evaluation.Status);
                    return false;
                }

                // Form onaylama
                evaluation.Status = "Approved";
                // ApprovedBy ve ApprovalDate property'leri entity'de yok, sadece Status güncelleniyor

                await _context.SaveChangesAsync();

                _logger.LogInformation("Değerlendirme formu başarıyla onaylandı - Form: {FormId}, ApprovedBy: {ApprovedBy}",
                    formId, approvedByUserId);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Değerlendirme formu onaylama hatası - Form: {FormId}", formId);
                return false;
            }
        }

        public async Task<StaffCompetencyReportDto> GenerateCompetencyReportAsync(string reportType, string scopeId, string period, string generatedByUserId)
        {
            try
            {
                _logger.LogInformation("Yetkinlik raporu oluşturuluyor - Type: {ReportType}, Scope: {ScopeId}, Period: {Period}",
                    reportType, scopeId, period);

                var query = _context.StaffCompetencyEvaluations
                    .Include(e => e.CompetencyRatings)
                    .ThenInclude(r => r.CompetencyDefinition)
                    .Where(e => !e.Disabled);

                // Period filtresi
                if (!string.IsNullOrEmpty(period))
                {
                    query = query.Where(e => e.EvaluationContextId == period);
                }

                // Scope filtresi (reportType'a göre)
                switch (reportType.ToLower())
                {
                    case "staff":
                        query = query.Where(e => e.AcademicianUniveristyUserId == scopeId);
                        break;
                    case "department":
                        query = query.Where(e => e.DepartmentId == scopeId);
                        break;
                    case "evaluator":
                        query = query.Where(e => e.EvaluatorUserId == scopeId);
                        break;
                        // "all" için ek filtre yok
                }

                var evaluations = await query.ToListAsync();

                var report = new StaffCompetencyReportDto
                {
                    ReportId = Guid.NewGuid().ToString(),
                    ReportType = reportType,
                    ReportTitle = $"{reportType} Yetkinlik Raporu",
                    ScopeId = scopeId,
                    ScopeName = $"Kapsam {scopeId}",
                    Period = period,
                    ReportDate = DateTime.UtcNow,
                    GeneratedByUserId = generatedByUserId,
                    GeneratedByUserName = $"Kullanıcı {generatedByUserId}",
                    ReportFormat = "JSON",

                    // Özet bilgiler
                    Summary = new StaffCompetencyReportSummaryDto
                    {
                        TotalEvaluations = evaluations.Count,
                        AverageOverallScore = evaluations.SelectMany(e => e.CompetencyRatings ?? new List<CompetencyRatingEntity>())
                            .Where(r => !string.IsNullOrEmpty(r.Rating))
                            .DefaultIfEmpty()
                            .Average(r => r != null ? ConvertRatingToNumeric(r.Rating) : 0)
                    },

                    Details = new List<StaffCompetencyReportDetailDto>(),
                    Charts = new List<StaffCompetencyReportChartDto>()
                };

                _logger.LogInformation("Yetkinlik raporu oluşturuldu - Report: {ReportId}, Evaluations: {Count}",
                    report.ReportId, evaluations.Count);

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Yetkinlik raporu oluşturma hatası - Type: {ReportType}", reportType);
                return new StaffCompetencyReportDto
                {
                    ReportId = Guid.NewGuid().ToString(),
                    ReportType = reportType,
                    ReportTitle = "Hata Raporu",
                    ScopeId = scopeId,
                    Period = period,
                    ReportDate = DateTime.UtcNow,
                    GeneratedByUserId = generatedByUserId,
                    Summary = new StaffCompetencyReportSummaryDto
                    {
                        TotalEvaluations = 0,
                        AverageOverallScore = 0
                    }
                };
            }
        }

        public async Task<double> CalculateOverallCompetencyScoreAsync(string staffId, string period)
        {
            try
            {
                _logger.LogInformation("Genel yetkinlik skoru hesaplanıyor - Staff: {StaffId}, Period: {Period}",
                    staffId, period);

                var evaluations = await _context.StaffCompetencyEvaluations
                    .Include(e => e.CompetencyRatings)
                    .Where(e => e.AcademicianUniveristyUserId == staffId &&
                               e.EvaluationContextId == period &&
                               !e.Disabled)
                    .ToListAsync();

                if (!evaluations.Any())
                {
                    _logger.LogWarning("Genel yetkinlik skoru hesaplama için değerlendirme bulunamadı - Staff: {StaffId}", staffId);
                    return 0.0;
                }

                var allRatings = evaluations
                    .SelectMany(e => e.CompetencyRatings ?? new List<CompetencyRatingEntity>())
                    .Where(r => !string.IsNullOrEmpty(r.Rating))
                    .ToList();

                if (!allRatings.Any())
                {
                    return 0.0;
                }

                // Basit ortalama hesaplama (ağırlıklandırma için gelecekte geliştirilebilir)
                var overallScore = allRatings.Average(r => ConvertRatingToNumeric(r.Rating));

                _logger.LogInformation("Genel yetkinlik skoru hesaplandı - Staff: {StaffId}, Score: {Score}",
                    staffId, overallScore);

                return overallScore;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Genel yetkinlik skoru hesaplama hatası - Staff: {StaffId}", staffId);
                return 0.0;
            }
        }

        public async Task<double> CalculateCompetencyGrowthRateAsync(string staffId, string currentPeriod, string previousPeriod)
        {
            try
            {
                _logger.LogInformation("Yetkinlik büyüme oranı hesaplanıyor - Staff: {StaffId}, Current: {Current}, Previous: {Previous}",
                    staffId, currentPeriod, previousPeriod);

                var currentScore = await CalculateOverallCompetencyScoreAsync(staffId, currentPeriod);
                var previousScore = await CalculateOverallCompetencyScoreAsync(staffId, previousPeriod);

                if (previousScore == 0)
                {
                    _logger.LogWarning("Önceki dönem skoru bulunamadı - büyüme oranı hesaplanamıyor");
                    return 0.0;
                }

                var growthRate = ((currentScore - previousScore) / previousScore) * 100;

                _logger.LogInformation("Yetkinlik büyüme oranı hesaplandı - Staff: {StaffId}, Growth: {Growth}%",
                    staffId, growthRate);

                return growthRate;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Yetkinlik büyüme oranı hesaplama hatası - Staff: {StaffId}", staffId);
                return 0.0;
            }
        }

        public async Task<bool> SynchronizeStaffCompetencyDataAsync(string staffId, string period)
        {
            try
            {
                _logger.LogInformation("Personel yetkinlik verileri senkronize ediliyor - Staff: {StaffId}, Period: {Period}",
                    staffId, period);

                // Basit senkronizasyon implementasyonu
                await Task.Delay(100); // Simulated sync operation

                _logger.LogInformation("Personel yetkinlik verileri senkronize edildi");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik verileri senkronizasyon hatası");
                return false;
            }
        }

        public async Task<bool> ClearStaffCompetencyCacheAsync(string staffId)
        {
            try
            {
                _logger.LogInformation("Personel yetkinlik cache'i temizleniyor - Staff: {StaffId}", staffId);

                // Basit cache temizleme implementasyonu
                await Task.Delay(50); // Simulated cache clear operation

                _logger.LogInformation("Personel yetkinlik cache'i temizlendi");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik cache temizleme hatası");
                return false;
            }
        }

        // Eksik interface method'ları - placeholder implementasyonlar
        public async Task<List<StaffCompetencyEvaluationEntity>> GetStaffCompetenciesByStaffIdAsync(string staffId, string? period, int? limit)
        {
            try
            {
                _logger.LogInformation("Personel yetkinlik değerlendirmeleri getiriliyor - Staff: {StaffId}, Period: {Period}, Limit: {Limit}",
                    staffId, period, limit);

                var query = _context.StaffCompetencyEvaluations
                    .Include(e => e.CompetencyRatings)
                    .ThenInclude(r => r.CompetencyDefinition)
                    .Where(e => e.AcademicianUniveristyUserId == staffId && !e.Disabled);

                // Period filtresi ekle
                if (!string.IsNullOrEmpty(period))
                {
                    query = query.Where(e => e.EvaluationContextId == period);
                }

                // Tarihe göre sırala (en yeni önce)
                query = query.OrderByDescending(e => e.SubmittedAt);

                // Limit uygula
                if (limit.HasValue && limit.Value > 0)
                {
                    query = query.Take(limit.Value);
                }

                var evaluations = await query.ToListAsync();

                _logger.LogInformation("Personel yetkinlik değerlendirmeleri getirildi - {Count} kayıt, Staff: {StaffId}",
                    evaluations.Count, staffId);

                return evaluations;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik değerlendirmeleri getirme hatası - Staff: {StaffId}", staffId);
                throw;
            }
        }

        public async Task<List<StaffCompetencyEvaluationEntity>> GetStaffCompetenciesByEvaluatorAsync(string evaluatorId, string? period)
        {
            try
            {
                _logger.LogInformation("Değerlendirici bazında yetkinlik değerlendirmeleri getiriliyor - Evaluator: {EvaluatorId}, Period: {Period}",
                    evaluatorId, period);

                var query = _context.StaffCompetencyEvaluations
                    .Include(e => e.CompetencyRatings)
                    .ThenInclude(r => r.CompetencyDefinition)
                    .Where(e => e.EvaluatorUserId == evaluatorId && !e.Disabled);

                // Period filtresi ekle
                if (!string.IsNullOrEmpty(period))
                {
                    query = query.Where(e => e.EvaluationContextId == period);
                }

                // Tarihe göre sırala (en yeni önce)
                var evaluations = await query
                    .OrderByDescending(e => e.SubmittedAt)
                    .ToListAsync();

                _logger.LogInformation("Değerlendirici bazında yetkinlik değerlendirmeleri getirildi - {Count} kayıt, Evaluator: {EvaluatorId}",
                    evaluations.Count, evaluatorId);

                return evaluations;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Değerlendirici bazında yetkinlik değerlendirmeleri getirme hatası - Evaluator: {EvaluatorId}", evaluatorId);
                throw;
            }
        }

        public async Task<List<StaffCompetencyEvaluationEntity>> GetStaffCompetenciesByStatusAsync(string status, string? departmentId)
        {
            try
            {
                _logger.LogInformation("Durum bazında yetkinlik değerlendirmeleri getiriliyor - Status: {Status}, Department: {DepartmentId}",
                    status, departmentId);

                var query = _context.StaffCompetencyEvaluations
                    .Include(e => e.CompetencyRatings)
                    .ThenInclude(r => r.CompetencyDefinition)
                    .Where(e => e.Status == status && !e.Disabled);

                // Department filtresi ekle
                if (!string.IsNullOrEmpty(departmentId))
                {
                    query = query.Where(e => e.DepartmentId == departmentId);
                }

                // Tarihe göre sırala (en yeni önce)
                var evaluations = await query
                    .OrderByDescending(e => e.SubmittedAt)
                    .ToListAsync();

                _logger.LogInformation("Durum bazında yetkinlik değerlendirmeleri getirildi - {Count} kayıt, Status: {Status}",
                    evaluations.Count, status);

                return evaluations;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Durum bazında yetkinlik değerlendirmeleri getirme hatası - Status: {Status}", status);
                throw;
            }
        }

        public Task<List<CompetencyTrendDataDto>> GetStaffCompetencyTrendDataAsync(string staffId, int periodCount)
        {
            return Task.FromResult(new List<CompetencyTrendDataDto>());
        }

        public async Task<Dictionary<string, double>> CalculateAverageCompetencyScoresAsync(string departmentId, string period, string? competencyArea)
        {
            try
            {
                _logger.LogInformation("Ortalama yetkinlik skorları hesaplanıyor - Department: {DepartmentId}, Period: {Period}, Area: {Area}",
                    departmentId, period, competencyArea);

                // Belirtilen bölüm ve dönem için tüm değerlendirmeleri getir
                var query = _context.StaffCompetencyEvaluations
                    .Include(e => e.CompetencyRatings)
                    .ThenInclude(r => r.CompetencyDefinition)
                    .Where(e => e.EvaluationContextId == period && !e.Disabled);

                if (!string.IsNullOrEmpty(departmentId))
                {
                    query = query.Where(e => e.DepartmentId == departmentId);
                }

                var evaluations = await query.ToListAsync();

                if (!evaluations.Any())
                {
                    _logger.LogInformation("Ortalama hesaplama için değerlendirme bulunamadı - Department: {DepartmentId}, Period: {Period}",
                        departmentId, period);
                    return new Dictionary<string, double>();
                }

                // Yetkinlik alanlarına göre ortalama skorları hesapla
                var ratingsQuery = evaluations
                    .SelectMany(e => e.CompetencyRatings ?? new List<CompetencyRatingEntity>())
                    .Where(r => r.CompetencyDefinition != null);

                // Belirli yetkinlik alanı filtresi varsa uygula
                if (!string.IsNullOrEmpty(competencyArea))
                {
                    ratingsQuery = ratingsQuery.Where(r => r.CompetencySystemId == competencyArea);
                }

                var averageScores = ratingsQuery
                    .GroupBy(r => new
                    {
                        CompetencyId = r.CompetencySystemId,
                        CompetencyName = r.CompetencyDefinition!.Name
                    })
                    .ToDictionary(
                        g => g.Key.CompetencyName,
                        g => g.Average(r => ConvertRatingToNumeric(r.Rating))
                    );

                // Genel ortalama ekle (sadece birden fazla yetkinlik alanı varsa)
                if (averageScores.Count > 1)
                {
                    averageScores["Genel Ortalama"] = averageScores.Values.Average();
                }

                _logger.LogInformation("Ortalama yetkinlik skorları hesaplandı - {Count} yetkinlik alanı, Department: {DepartmentId}",
                    averageScores.Count, departmentId);

                return averageScores;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ortalama yetkinlik skorları hesaplama hatası - Department: {DepartmentId}, Period: {Period}",
                    departmentId, period);
                throw;
            }
        }

        public Task<Dictionary<string, int>> CalculateDepartmentCompetencyDistributionAsync(string departmentId, string period)
        {
            return Task.FromResult(new Dictionary<string, int>());
        }

        public Task<List<StaffCompetencyEvaluationEntity>> SearchStaffCompetenciesAsync(string searchTerm, string? departmentId, string? period)
        {
            return Task.FromResult(new List<StaffCompetencyEvaluationEntity>());
        }

        public Task<List<StaffCompetencyEvaluationEntity>> SearchByCompetencyScoreAsync(double minScore, double maxScore, string? departmentId, string? period, string competencyArea)
        {
            return Task.FromResult(new List<StaffCompetencyEvaluationEntity>());
        }

        public async Task<List<StaffCompetencyEvaluationEntity>> GetTopPerformersAsync(string departmentId, string period, string? competencyArea, int limit)
        {
            try
            {
                _logger.LogInformation("En yüksek performanslı personeller getiriliyor - Department: {DepartmentId}, Period: {Period}, Area: {Area}, Limit: {Limit}",
                    departmentId, period, competencyArea, limit);

                var query = _context.StaffCompetencyEvaluations
                    .Include(e => e.CompetencyRatings)
                    .ThenInclude(r => r.CompetencyDefinition)
                    .Where(e => !e.Disabled);

                // Department filtresi ekle
                if (!string.IsNullOrEmpty(departmentId))
                {
                    query = query.Where(e => e.DepartmentId == departmentId);
                }

                // Period filtresi ekle
                if (!string.IsNullOrEmpty(period))
                {
                    query = query.Where(e => e.EvaluationContextId == period);
                }

                var evaluations = await query.ToListAsync();

                // Personel bazında ortalama skorları hesapla ve sırala
                var topPerformers = evaluations
                    .GroupBy(e => e.AcademicianUniveristyUserId)
                    .Select(g => new
                    {
                        StaffId = g.Key,
                        Evaluations = g.ToList(),
                        OverallScore = g.SelectMany(e => e.CompetencyRatings ?? new List<CompetencyRatingEntity>())
                            .Where(r => string.IsNullOrEmpty(competencyArea) || r.CompetencySystemId == competencyArea)
                            .Where(r => r.CompetencyDefinition != null)
                            .DefaultIfEmpty()
                            .Average(r => r != null ? ConvertRatingToNumeric(r.Rating) : 0)
                    })
                    .Where(s => !double.IsNaN(s.OverallScore) && s.OverallScore > 0)
                    .OrderByDescending(s => s.OverallScore)
                    .Take(limit)
                    .SelectMany(s => s.Evaluations)
                    .OrderByDescending(e => e.SubmittedAt)
                    .ToList();

                _logger.LogInformation("En yüksek performanslı personeller getirildi - {Count} değerlendirme",
                    topPerformers.Count);

                return topPerformers;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "En yüksek performanslı personeller getirme hatası");
                throw;
            }
        }

        public async Task<ValidationResultDto> ValidateStaffCompetencyAsync(StaffCompetencyEvaluationEntity entity)
        {
            try
            {
                _logger.LogInformation("Personel yetkinlik değerlendirmesi doğrulanıyor - ID: {Id}", entity?.Id);

                var validationResult = new ValidationResultDto
                {
                    IsValid = true,
                    Errors = new List<string>()
                };

                // Null check
                if (entity == null)
                {
                    validationResult.IsValid = false;
                    validationResult.Errors.Add("Değerlendirme entity'si null olamaz");
                    return validationResult;
                }

                // Required field validations
                if (string.IsNullOrEmpty(entity.AcademicianUniveristyUserId))
                {
                    validationResult.IsValid = false;
                    validationResult.Errors.Add("Personel ID zorunludur");
                }

                if (string.IsNullOrEmpty(entity.EvaluationContextId))
                {
                    validationResult.IsValid = false;
                    validationResult.Errors.Add("Değerlendirme context ID zorunludur");
                }

                if (string.IsNullOrEmpty(entity.DepartmentId))
                {
                    validationResult.IsValid = false;
                    validationResult.Errors.Add("Bölüm ID zorunludur");
                }

                // CompetencyRatings validation
                if (entity.CompetencyRatings == null || !entity.CompetencyRatings.Any())
                {
                    validationResult.IsValid = false;
                    validationResult.Errors.Add("En az bir yetkinlik değerlendirmesi gereklidir");
                }
                else
                {
                    // Her rating için validation
                    foreach (var rating in entity.CompetencyRatings)
                    {
                        if (string.IsNullOrEmpty(rating.CompetencySystemId))
                        {
                            validationResult.IsValid = false;
                            validationResult.Errors.Add("Yetkinlik sistem ID'si zorunludur");
                        }

                        if (string.IsNullOrEmpty(rating.Rating))
                        {
                            validationResult.IsValid = false;
                            validationResult.Errors.Add("Yetkinlik puanı zorunludur");
                        }
                        else
                        {
                            // Rating değeri validation (1-5 arası olmalı)
                            var numericRating = ConvertRatingToNumeric(rating.Rating);
                            if (numericRating < 1 || numericRating > 5)
                            {
                                validationResult.IsValid = false;
                                validationResult.Errors.Add($"Yetkinlik puanı 1-5 arasında olmalıdır. Geçersiz değer: {rating.Rating}");
                            }
                        }
                    }
                }

                // Status validation
                var validStatuses = new[] { "Draft", "Submitted", "Approved", "Rejected", "Archived" };
                if (!validStatuses.Contains(entity.Status))
                {
                    validationResult.IsValid = false;
                    validationResult.Errors.Add($"Geçersiz durum: {entity.Status}. Geçerli durumlar: {string.Join(", ", validStatuses)}");
                }

                // Date validation
                if (entity.SubmittedAt > DateTime.UtcNow.AddDays(1))
                {
                    validationResult.IsValid = false;
                    validationResult.Errors.Add("Gönderim tarihi gelecekte olamaz");
                }

                _logger.LogInformation("Personel yetkinlik değerlendirmesi doğrulandı - Valid: {IsValid}, Errors: {ErrorCount}",
                    validationResult.IsValid, validationResult.Errors.Count);

                return validationResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik değerlendirmesi doğrulama hatası");
                return new ValidationResultDto
                {
                    IsValid = false,
                    Errors = new List<string> { "Doğrulama sırasında beklenmeyen bir hata oluştu" }
                };
            }
        }

        public async Task<bool> CheckDuplicateEvaluationAsync(string staffId, string evaluatorId, string period, string? excludeId)
        {
            try
            {
                _logger.LogInformation("Duplicate değerlendirme kontrolü yapılıyor - Staff: {StaffId}, Evaluator: {EvaluatorId}, Period: {Period}",
                    staffId, evaluatorId, period);

                var query = _context.StaffCompetencyEvaluations
                    .Where(e => e.AcademicianUniveristyUserId == staffId &&
                               e.EvaluatorUserId == evaluatorId &&
                               e.EvaluationContextId == period &&
                               !e.Disabled);

                // Exclude belirli bir ID'yi hariç tutmak için (güncelleme senaryolarında)
                if (!string.IsNullOrEmpty(excludeId))
                {
                    query = query.Where(e => e.Id != excludeId);
                }

                var duplicateExists = await query.AnyAsync();

                _logger.LogInformation("Duplicate değerlendirme kontrolü tamamlandı - Duplicate: {DuplicateExists}",
                    duplicateExists);

                return duplicateExists;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Duplicate değerlendirme kontrolü hatası - Staff: {StaffId}, Evaluator: {EvaluatorId}",
                    staffId, evaluatorId);
                throw;
            }
        }

        public async Task<bool> ValidateCompetencyScoresAsync(StaffCompetencyEvaluationEntity entity)
        {
            try
            {
                _logger.LogInformation("Yetkinlik skorları doğrulanıyor - Entity ID: {Id}", entity?.Id);

                // Null check
                if (entity?.CompetencyRatings == null || !entity.CompetencyRatings.Any())
                {
                    _logger.LogWarning("Yetkinlik skorları bulunamadı");
                    return false;
                }

                // Her rating için detaylı validation
                foreach (var rating in entity.CompetencyRatings)
                {
                    // CompetencySystemId validation
                    if (string.IsNullOrEmpty(rating.CompetencySystemId))
                    {
                        _logger.LogWarning("Yetkinlik sistem ID'si boş");
                        return false;
                    }

                    // Rating value validation
                    if (string.IsNullOrEmpty(rating.Rating))
                    {
                        _logger.LogWarning("Yetkinlik puanı boş - CompetencyId: {CompetencyId}", rating.CompetencySystemId);
                        return false;
                    }

                    // Numeric rating validation (1-5 scale)
                    var numericRating = ConvertRatingToNumeric(rating.Rating);
                    if (numericRating < 1 || numericRating > 5)
                    {
                        _logger.LogWarning("Geçersiz yetkinlik puanı: {Rating} - CompetencyId: {CompetencyId}",
                            rating.Rating, rating.CompetencySystemId);
                        return false;
                    }

                    // Rating scale consistency check
                    if (!IsValidRatingScale(rating.Rating))
                    {
                        _logger.LogWarning("Geçersiz rating formatı: {Rating} - CompetencyId: {CompetencyId}",
                            rating.Rating, rating.CompetencySystemId);
                        return false;
                    }

                    // CompetencyDefinition existence check (if available)
                    if (rating.CompetencyDefinition == null)
                    {
                        // CompetencyDefinition yoksa veritabanından kontrol et
                        var competencyExists = await _context.StaffCompetencyDefinitions
                            .AnyAsync(cd => cd.CompetencySystemId == rating.CompetencySystemId && cd.IsActive);

                        if (!competencyExists)
                        {
                            _logger.LogWarning("Yetkinlik tanımı bulunamadı: {CompetencyId}", rating.CompetencySystemId);
                            return false;
                        }
                    }
                }

                // Duplicate competency check
                var duplicateCompetencies = entity.CompetencyRatings
                    .GroupBy(r => r.CompetencySystemId)
                    .Where(g => g.Count() > 1)
                    .Select(g => g.Key)
                    .ToList();

                if (duplicateCompetencies.Any())
                {
                    _logger.LogWarning("Duplicate yetkinlik puanları tespit edildi: {Duplicates}",
                        string.Join(", ", duplicateCompetencies));
                    return false;
                }

                _logger.LogInformation("Yetkinlik skorları başarıyla doğrulandı - {Count} rating",
                    entity.CompetencyRatings.Count);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Yetkinlik skorları doğrulama hatası");
                return false;
            }
        }

        /// <summary>
        /// Rating scale formatının geçerli olup olmadığını kontrol eder
        /// </summary>
        private bool IsValidRatingScale(string rating)
        {
            // Numeric ratings (1-5)
            if (int.TryParse(rating, out int numericValue))
            {
                return numericValue >= 1 && numericValue <= 5;
            }

            // Text-based ratings
            var validTextRatings = new[] { "Excellent", "Good", "Average", "Poor", "Very Poor", "Mükemmel", "İyi", "Ortalama", "Zayıf", "Çok Zayıf" };
            return validTextRatings.Contains(rating, StringComparer.OrdinalIgnoreCase);
        }

        public async Task<int> GetStaffCompetencyCountAsync(string? departmentId, string? period, string? status)
        {
            try
            {
                _logger.LogInformation("Personel yetkinlik sayısı getiriliyor - Dept: {Dept}, Period: {Period}, Status: {Status}",
                    departmentId, period, status);

                var query = _context.StaffCompetencyEvaluations.Where(e => !e.Disabled);

                if (!string.IsNullOrEmpty(departmentId))
                    query = query.Where(e => e.DepartmentId == departmentId);

                if (!string.IsNullOrEmpty(period))
                    query = query.Where(e => e.EvaluationContextId == period);

                if (!string.IsNullOrEmpty(status))
                    query = query.Where(e => e.Status == status);

                var count = await query.CountAsync();

                _logger.LogInformation("Personel yetkinlik sayısı: {Count}", count);
                return count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik sayısı getirme hatası");
                throw;
            }
        }

        /// <summary>
        /// Staff competency evaluations bulk status update - Enhanced with BulkOperationExtensions
        /// </summary>
        public async Task<BulkOperationResult> BulkUpdateStatusAsync(
            List<string> ids,
            string newStatus,
            string updatedByUserId,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("StaffCompetencyStore bulk status update başlatılıyor - Count: {Count}, Status: {Status}",
                    ids.Count, newStatus);

                // Get entities to update
                var evaluations = await _context.StaffCompetencyEvaluations
                    .Where(e => ids.Contains(e.Id) && !e.Disabled)
                    .ToListAsync(cancellationToken);

                if (!evaluations.Any())
                {
                    _logger.LogWarning("Status update için entity bulunamadı - IDs: {IDs}", string.Join(", ", ids));
                    return new BulkOperationResult
                    {
                        Success = true,
                        ProcessedCount = 0,
                        ElapsedTime = TimeSpan.Zero
                    };
                }

                // Apply status updates with business logic
                foreach (var evaluation in evaluations)
                {
                    evaluation.Status = newStatus;
                    // Apply audit fields and business logic
                    evaluation.ApplyStaffCompetencyBusinessLogic();
                }

                // Apply audit fields
                evaluations.ApplyAuditFields(DateTime.UtcNow, updatedByUserId, isUpdate: true);

                // Validate entities before update
                var validationErrors = ValidateEntitiesForStatusUpdate(evaluations, newStatus);
                if (validationErrors.Any())
                {
                    _logger.LogWarning("Bulk status update validation hatası - Errors: {Errors}", string.Join(", ", validationErrors));
                    return new BulkOperationResult
                    {
                        Success = false,
                        ProcessedCount = 0,
                        ErrorMessage = $"Validation errors: {string.Join(", ", validationErrors)}"
                    };
                }

                // Use enhanced BulkOperationExtensions
                var result = await _context.BulkUpdateAsync(
                    evaluations,
                    performanceMonitor: _performanceMonitoringService,
                    cancellationToken: cancellationToken);

                if (result.Success)
                {
                    _logger.LogInformation("StaffCompetencyStore bulk status update tamamlandı - Processed: {Count}, Time: {ElapsedTime}ms",
                        result.ProcessedCount, result.ElapsedTime.TotalMilliseconds);
                }
                else
                {
                    _logger.LogError("StaffCompetencyStore bulk status update başarısız - Error: {Error}", result.ErrorMessage);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "StaffCompetencyStore bulk status update işlemi sırasında hata");
                return new BulkOperationResult
                {
                    Success = false,
                    ProcessedCount = 0,
                    ErrorMessage = ex.Message,
                    ElapsedTime = TimeSpan.Zero
                };
            }
        }

        /// <summary>
        /// Archive old evaluations - Enhanced with BulkOperationExtensions
        /// </summary>
        public async Task<BulkOperationResult> ArchiveOldEvaluationsAsync(
            DateTime cutoffDate,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("StaffCompetencyStore bulk archive başlatılıyor - Cutoff: {CutoffDate}", cutoffDate);

                // Get evaluations to archive
                var evaluationsToArchive = await _context.StaffCompetencyEvaluations
                    .Where(e => e.SubmittedAt < cutoffDate && e.Status != "Archived" && !e.Disabled)
                    .ToListAsync(cancellationToken);

                if (!evaluationsToArchive.Any())
                {
                    _logger.LogInformation("Archive için eski değerlendirme bulunamadı - Cutoff: {CutoffDate}", cutoffDate);
                    return new BulkOperationResult
                    {
                        Success = true,
                        ProcessedCount = 0,
                        ElapsedTime = TimeSpan.Zero
                    };
                }

                // Apply archive status and business logic
                foreach (var evaluation in evaluationsToArchive)
                {
                    evaluation.Status = "Archived";
                    evaluation.ApplyStaffCompetencyBusinessLogic();
                }

                // Apply audit fields
                evaluationsToArchive.ApplyAuditFields(DateTime.UtcNow, "system", isUpdate: true);

                // Use enhanced BulkOperationExtensions
                var result = await _context.BulkUpdateAsync(
                    evaluationsToArchive,
                    performanceMonitor: _performanceMonitoringService,
                    cancellationToken: cancellationToken);

                if (result.Success)
                {
                    _logger.LogInformation("StaffCompetencyStore bulk archive tamamlandı - Processed: {Count}, Time: {ElapsedTime}ms",
                        result.ProcessedCount, result.ElapsedTime.TotalMilliseconds);
                }
                else
                {
                    _logger.LogError("StaffCompetencyStore bulk archive başarısız - Error: {Error}", result.ErrorMessage);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "StaffCompetencyStore bulk archive işlemi sırasında hata");
                return new BulkOperationResult
                {
                    Success = false,
                    ProcessedCount = 0,
                    ErrorMessage = ex.Message,
                    ElapsedTime = TimeSpan.Zero
                };
            }
        }

        /// <summary>
        /// Staff competency evaluations bulk insert - Enhanced with BulkOperationExtensions
        /// </summary>
        public async Task<BulkOperationResult> BulkInsertAsync(
            List<StaffCompetencyEvaluationEntity> entities,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("StaffCompetencyStore bulk insert başlatılıyor - Entity Count: {Count}", entities.Count);

                // Apply business logic
                foreach (var entity in entities)
                {
                    entity.ApplyStaffCompetencyBusinessLogic();
                }

                // Apply audit fields
                entities.ApplyAuditFields(DateTime.UtcNow, "system");

                // Validate entities before insert
                var validationErrors = ValidateEntitiesForInsert(entities);
                if (validationErrors.Any())
                {
                    _logger.LogWarning("Bulk insert validation hatası - Errors: {Errors}", string.Join(", ", validationErrors));
                    return new BulkOperationResult
                    {
                        Success = false,
                        ProcessedCount = 0,
                        ErrorMessage = $"Validation errors: {string.Join(", ", validationErrors)}"
                    };
                }

                // Use enhanced BulkOperationExtensions
                var result = await _context.BulkInsertAsync(
                    entities,
                    performanceMonitor: _performanceMonitoringService,
                    cancellationToken: cancellationToken);

                if (result.Success)
                {
                    _logger.LogInformation("StaffCompetencyStore bulk insert tamamlandı - Processed: {Count}, Time: {ElapsedTime}ms",
                        result.ProcessedCount, result.ElapsedTime.TotalMilliseconds);
                }
                else
                {
                    _logger.LogError("StaffCompetencyStore bulk insert başarısız - Error: {Error}", result.ErrorMessage);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "StaffCompetencyStore bulk insert işlemi sırasında hata");
                return new BulkOperationResult
                {
                    Success = false,
                    ProcessedCount = 0,
                    ErrorMessage = ex.Message,
                    ElapsedTime = TimeSpan.Zero
                };
            }
        }

        /// <summary>
        /// Staff competency evaluations bulk delete - Enhanced with BulkOperationExtensions
        /// </summary>
        public async Task<BulkOperationResult> BulkDeleteAsync(
            List<StaffCompetencyEvaluationEntity> entities,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("StaffCompetencyStore bulk delete başlatılıyor - Entity Count: {Count}", entities.Count);

                // Validate entities before delete
                var validationErrors = ValidateEntitiesForDelete(entities);
                if (validationErrors.Any())
                {
                    _logger.LogWarning("Bulk delete validation hatası - Errors: {Errors}", string.Join(", ", validationErrors));
                    return new BulkOperationResult
                    {
                        Success = false,
                        ProcessedCount = 0,
                        ErrorMessage = $"Validation errors: {string.Join(", ", validationErrors)}"
                    };
                }

                // Use enhanced BulkOperationExtensions
                var result = await _context.BulkDeleteAsync(
                    entities,
                    performanceMonitor: _performanceMonitoringService,
                    cancellationToken: cancellationToken);

                if (result.Success)
                {
                    _logger.LogInformation("StaffCompetencyStore bulk delete tamamlandı - Processed: {Count}, Time: {ElapsedTime}ms",
                        result.ProcessedCount, result.ElapsedTime.TotalMilliseconds);
                }
                else
                {
                    _logger.LogError("StaffCompetencyStore bulk delete başarısız - Error: {Error}", result.ErrorMessage);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "StaffCompetencyStore bulk delete işlemi sırasında hata");
                return new BulkOperationResult
                {
                    Success = false,
                    ProcessedCount = 0,
                    ErrorMessage = ex.Message,
                    ElapsedTime = TimeSpan.Zero
                };
            }
        }

        #region Private Validation Methods

        /// <summary>
        /// Status update için entity validasyonu
        /// </summary>
        private List<string> ValidateEntitiesForStatusUpdate(List<StaffCompetencyEvaluationEntity> entities, string newStatus)
        {
            var errors = new List<string>();

            var validStatuses = new[] { "Draft", "Submitted", "InReview", "Approved", "Rejected", "Archived" };
            if (!validStatuses.Contains(newStatus))
            {
                errors.Add($"Geçersiz status: {newStatus}");
            }

            foreach (var entity in entities)
            {
                if (string.IsNullOrEmpty(entity.Id))
                    errors.Add("Status update için entity Id boş olamaz");

                if (entity.Disabled)
                    errors.Add($"Disabled entity status güncellenemez - Entity: {entity.Id}");
            }

            return errors;
        }

        /// <summary>
        /// Insert için entity validasyonu
        /// </summary>
        private List<string> ValidateEntitiesForInsert(List<StaffCompetencyEvaluationEntity> entities)
        {
            var errors = new List<string>();

            foreach (var entity in entities)
            {
                if (string.IsNullOrEmpty(entity.StaffId))
                    errors.Add($"StaffId boş olamaz - Entity: {entity.Id}");

                if (string.IsNullOrEmpty(entity.CompetencyId))
                    errors.Add($"CompetencyId boş olamaz - Entity: {entity.Id}");

                if (entity.Score < 0 || entity.Score > 100)
                    errors.Add($"Score 0-100 arasında olmalı - Entity: {entity.Id}, Score: {entity.Score}");
            }

            return errors;
        }

        /// <summary>
        /// Delete için entity validasyonu
        /// </summary>
        private List<string> ValidateEntitiesForDelete(List<StaffCompetencyEvaluationEntity> entities)
        {
            var errors = new List<string>();

            foreach (var entity in entities)
            {
                if (string.IsNullOrEmpty(entity.Id))
                    errors.Add("Delete için entity Id boş olamaz");

                if (entity.Status == "Approved")
                    errors.Add($"Approved entity silinemez - Entity: {entity.Id}");
            }

            return errors;
        }

        #endregion



        #endregion
    }
}
