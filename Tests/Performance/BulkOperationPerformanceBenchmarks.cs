using System.Diagnostics;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using Xunit.Abstractions;
using AcademicPerformance.DbContexts;
using AcademicPerformance.Extensions;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Services.Interfaces;
using AcademicPerformance.Stores;

namespace AcademicPerformance.Tests.Performance;

/// <summary>
/// Performance benchmark tests comparing before/after refactoring
/// Tests demonstrate the performance improvements achieved through BulkOperationExtensions
/// </summary>
public class BulkOperationPerformanceBenchmarks : IDisposable
{
    private readonly ITestOutputHelper _output;
    private readonly AcademicPerformanceDbContext _context;
    private readonly Mock<IPerformanceMonitoringService> _mockPerformanceService;
    private readonly ILogger<DepartmentPerformanceStore> _logger;

    public BulkOperationPerformanceBenchmarks(ITestOutputHelper output)
    {
        _output = output;

        // Setup in-memory database
        var options = new DbContextOptionsBuilder<AcademicPerformanceDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new AcademicPerformanceDbContext(options);
        _mockPerformanceService = new Mock<IPerformanceMonitoringService>();

        // Setup logger
        var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        _logger = loggerFactory.CreateLogger<DepartmentPerformanceStore>();
    }

    [Fact]
    public async Task BulkInsert_PerformanceComparison_ShouldShowSignificantImprovement()
    {
        // Arrange
        const int entityCount = 1000;
        var entities = GenerateTestEntities(entityCount);

        // Test old approach (simulated)
        var oldApproachTime = await MeasureOldApproachInsert(entities);

        // Test new approach with BulkOperationExtensions
        var newApproachTime = await MeasureNewApproachInsert(entities);

        // Assert and log results
        var improvementRatio = (double)oldApproachTime.TotalMilliseconds / newApproachTime.TotalMilliseconds;
        
        _output.WriteLine($"=== BULK INSERT PERFORMANCE COMPARISON ===");
        _output.WriteLine($"Entity Count: {entityCount:N0}");
        _output.WriteLine($"Old Approach Time: {oldApproachTime.TotalMilliseconds:F2}ms");
        _output.WriteLine($"New Approach Time: {newApproachTime.TotalMilliseconds:F2}ms");
        _output.WriteLine($"Performance Improvement: {improvementRatio:F1}x faster");
        _output.WriteLine($"Time Saved: {(oldApproachTime - newApproachTime).TotalMilliseconds:F2}ms");

        // Assert significant improvement (at least 2x faster)
        Assert.True(improvementRatio >= 2.0, 
            $"Expected at least 2x improvement, got {improvementRatio:F1}x");
    }

    [Fact]
    public async Task BulkUpdate_PerformanceComparison_ShouldShowSignificantImprovement()
    {
        // Arrange
        const int entityCount = 500;
        var entities = GenerateTestEntities(entityCount);

        // Insert entities first
        await _context.DepartmentPerformances.AddRangeAsync(entities);
        await _context.SaveChangesAsync();

        // Modify entities for update
        foreach (var entity in entities)
        {
            entity.PerformanceScore = entity.PerformanceScore + 10;
            entity.UpdatedAt = DateTime.UtcNow;
        }

        // Test old approach (simulated)
        var oldApproachTime = await MeasureOldApproachUpdate(entities);

        // Reset entities for new approach test
        foreach (var entity in entities)
        {
            entity.PerformanceScore = entity.PerformanceScore - 5;
        }

        // Test new approach with BulkOperationExtensions
        var newApproachTime = await MeasureNewApproachUpdate(entities);

        // Assert and log results
        var improvementRatio = (double)oldApproachTime.TotalMilliseconds / newApproachTime.TotalMilliseconds;
        
        _output.WriteLine($"=== BULK UPDATE PERFORMANCE COMPARISON ===");
        _output.WriteLine($"Entity Count: {entityCount:N0}");
        _output.WriteLine($"Old Approach Time: {oldApproachTime.TotalMilliseconds:F2}ms");
        _output.WriteLine($"New Approach Time: {newApproachTime.TotalMilliseconds:F2}ms");
        _output.WriteLine($"Performance Improvement: {improvementRatio:F1}x faster");
        _output.WriteLine($"Time Saved: {(oldApproachTime - newApproachTime).TotalMilliseconds:F2}ms");

        // Assert significant improvement (at least 3x faster for updates)
        Assert.True(improvementRatio >= 3.0, 
            $"Expected at least 3x improvement, got {improvementRatio:F1}x");
    }

    [Fact]
    public async Task MemoryUsage_Comparison_ShouldShowReduction()
    {
        // Arrange
        const int entityCount = 2000;
        var entities = GenerateTestEntities(entityCount);

        // Measure memory before operations
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();
        var memoryBefore = GC.GetTotalMemory(false);

        // Test old approach memory usage
        var oldMemoryUsage = await MeasureOldApproachMemoryUsage(entities);

        // Clean up and measure new approach
        _context.ChangeTracker.Clear();
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();

        var newMemoryUsage = await MeasureNewApproachMemoryUsage(entities);

        // Calculate memory improvement
        var memoryReduction = ((double)(oldMemoryUsage - newMemoryUsage) / oldMemoryUsage) * 100;

        _output.WriteLine($"=== MEMORY USAGE COMPARISON ===");
        _output.WriteLine($"Entity Count: {entityCount:N0}");
        _output.WriteLine($"Old Approach Memory: {oldMemoryUsage / 1024 / 1024:F2} MB");
        _output.WriteLine($"New Approach Memory: {newMemoryUsage / 1024 / 1024:F2} MB");
        _output.WriteLine($"Memory Reduction: {memoryReduction:F1}%");
        _output.WriteLine($"Memory Saved: {(oldMemoryUsage - newMemoryUsage) / 1024 / 1024:F2} MB");

        // Assert significant memory reduction (at least 30%)
        Assert.True(memoryReduction >= 30.0, 
            $"Expected at least 30% memory reduction, got {memoryReduction:F1}%");
    }

    [Fact]
    public async Task BatchProcessing_EfficiencyTest_ShouldProcessInOptimalBatches()
    {
        // Arrange
        const int entityCount = 5000;
        var entities = GenerateTestEntities(entityCount);

        var store = new DepartmentPerformanceStore(_context, _logger, _mockPerformanceService.Object);

        // Act
        var stopwatch = Stopwatch.StartNew();
        var result = await store.BulkInsertAsync(entities, CancellationToken.None);
        stopwatch.Stop();

        // Assert
        Assert.True(result.Success);
        Assert.Equal(entityCount, result.ProcessedCount);

        // Verify batch processing was used (should be much faster than individual inserts)
        var expectedMaxTime = entityCount * 0.1; // 0.1ms per entity is very generous for batch processing
        Assert.True(stopwatch.ElapsedMilliseconds < expectedMaxTime,
            $"Batch processing took {stopwatch.ElapsedMilliseconds}ms, expected less than {expectedMaxTime}ms");

        _output.WriteLine($"=== BATCH PROCESSING EFFICIENCY ===");
        _output.WriteLine($"Entity Count: {entityCount:N0}");
        _output.WriteLine($"Processing Time: {stopwatch.ElapsedMilliseconds}ms");
        _output.WriteLine($"Throughput: {entityCount / (stopwatch.ElapsedMilliseconds / 1000.0):F0} entities/second");

        // Verify performance monitoring was called for batch progress
        _mockPerformanceService.Verify(x => x.ReportBatchProgressAsync(
            It.IsAny<string>(), It.IsAny<int>(), It.IsAny<int>()), Times.AtLeastOnce);
    }

    #region Helper Methods

    private List<DepartmentPerformanceEntity> GenerateTestEntities(int count)
    {
        var entities = new List<DepartmentPerformanceEntity>();
        var random = new Random(42); // Fixed seed for consistent results

        for (int i = 0; i < count; i++)
        {
            entities.Add(new DepartmentPerformanceEntity
            {
                Id = Guid.NewGuid().ToString(),
                DepartmentId = $"dept_{i % 10}", // 10 different departments
                AcademicYear = "2023-2024",
                Semester = i % 2 == 0 ? "Fall" : "Spring",
                PerformanceScore = random.Next(60, 100),
                StudentCount = random.Next(50, 200),
                CourseCount = random.Next(10, 50),
                AverageGPA = Math.Round(random.NextDouble() * 2 + 2, 2), // 2.0 - 4.0
                CreatedAt = DateTime.UtcNow.AddDays(-random.Next(1, 365)),
                IsActive = true
            });
        }

        return entities;
    }

    private async Task<TimeSpan> MeasureOldApproachInsert(List<DepartmentPerformanceEntity> entities)
    {
        var stopwatch = Stopwatch.StartNew();

        // Simulate old approach: individual SaveChanges calls
        foreach (var entity in entities)
        {
            entity.CreatedAt = DateTime.UtcNow;
            _context.DepartmentPerformances.Add(entity);
            await _context.SaveChangesAsync(); // Individual save - very inefficient
        }

        stopwatch.Stop();
        _context.ChangeTracker.Clear(); // Clean up
        return stopwatch.Elapsed;
    }

    private async Task<TimeSpan> MeasureNewApproachInsert(List<DepartmentPerformanceEntity> entities)
    {
        var store = new DepartmentPerformanceStore(_context, _logger, _mockPerformanceService.Object);
        
        var stopwatch = Stopwatch.StartNew();
        var result = await store.BulkInsertAsync(entities, CancellationToken.None);
        stopwatch.Stop();

        Assert.True(result.Success);
        return stopwatch.Elapsed;
    }

    private async Task<TimeSpan> MeasureOldApproachUpdate(List<DepartmentPerformanceEntity> entities)
    {
        var stopwatch = Stopwatch.StartNew();

        // Simulate old approach: individual updates with change tracking
        foreach (var entity in entities)
        {
            _context.DepartmentPerformances.Update(entity);
            await _context.SaveChangesAsync(); // Individual save
        }

        stopwatch.Stop();
        return stopwatch.Elapsed;
    }

    private async Task<TimeSpan> MeasureNewApproachUpdate(List<DepartmentPerformanceEntity> entities)
    {
        var store = new DepartmentPerformanceStore(_context, _logger, _mockPerformanceService.Object);
        
        var stopwatch = Stopwatch.StartNew();
        var result = await store.BulkUpdateAsync(entities, CancellationToken.None);
        stopwatch.Stop();

        Assert.True(result.Success);
        return stopwatch.Elapsed;
    }

    private async Task<long> MeasureOldApproachMemoryUsage(List<DepartmentPerformanceEntity> entities)
    {
        var memoryBefore = GC.GetTotalMemory(false);

        // Simulate old approach with change tracking enabled
        _context.DepartmentPerformances.AddRange(entities);
        await _context.SaveChangesAsync();
        // Don't clear change tracker - simulates old approach

        var memoryAfter = GC.GetTotalMemory(false);
        return memoryAfter - memoryBefore;
    }

    private async Task<long> MeasureNewApproachMemoryUsage(List<DepartmentPerformanceEntity> entities)
    {
        var memoryBefore = GC.GetTotalMemory(false);

        // Use new approach with change tracker clearing
        var result = await _context.BulkInsertAsync(entities, performanceMonitor: _mockPerformanceService.Object);
        Assert.True(result.Success);

        var memoryAfter = GC.GetTotalMemory(false);
        return memoryAfter - memoryBefore;
    }

    #endregion

    public void Dispose()
    {
        _context?.Dispose();
    }
}
