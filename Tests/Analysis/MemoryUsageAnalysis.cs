using System.Diagnostics;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using Xunit.Abstractions;
using AcademicPerformance.DbContexts;
using AcademicPerformance.Extensions;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Services.Interfaces;
using AcademicPerformance.Stores;

namespace AcademicPerformance.Tests.Analysis;

/// <summary>
/// Comprehensive memory usage analysis for bulk operations
/// Validates memory optimization improvements and identifies potential memory leaks
/// </summary>
public class MemoryUsageAnalysis : IDisposable
{
    private readonly ITestOutputHelper _output;
    private readonly AcademicPerformanceDbContext _context;
    private readonly Mock<IPerformanceMonitoringService> _mockPerformanceService;
    private readonly ILogger<DepartmentPerformanceStore> _logger;

    public MemoryUsageAnalysis(ITestOutputHelper output)
    {
        _output = output;

        var options = new DbContextOptionsBuilder<AcademicPerformanceDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new AcademicPerformanceDbContext(options);
        _mockPerformanceService = new Mock<IPerformanceMonitoringService>();

        var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        _logger = loggerFactory.CreateLogger<DepartmentPerformanceStore>();
    }

    [Fact]
    public async Task MemoryUsage_ChangeTrackerOptimization_ShouldShowSignificantReduction()
    {
        // Arrange
        const int entityCount = 5000;
        var entities = GenerateTestEntities(entityCount);

        _output.WriteLine($"=== CHANGE TRACKER OPTIMIZATION ANALYSIS ===");
        _output.WriteLine($"Testing memory usage with {entityCount:N0} entities");

        // Test WITHOUT change tracker optimization (old approach)
        var memoryWithoutOptimization = await MeasureMemoryWithoutOptimization(entities);

        // Clean up and test WITH change tracker optimization (new approach)
        _context.ChangeTracker.Clear();
        ForceGarbageCollection();

        var memoryWithOptimization = await MeasureMemoryWithOptimization(entities);

        // Calculate improvement
        var memoryReduction = ((double)(memoryWithoutOptimization - memoryWithOptimization) / memoryWithoutOptimization) * 100;
        var memoryReductionMB = (memoryWithoutOptimization - memoryWithOptimization) / 1024.0 / 1024.0;

        _output.WriteLine($"📊 Memory without optimization: {memoryWithoutOptimization / 1024.0 / 1024.0:F2} MB");
        _output.WriteLine($"📊 Memory with optimization: {memoryWithOptimization / 1024.0 / 1024.0:F2} MB");
        _output.WriteLine($"✅ Memory reduction: {memoryReduction:F1}% ({memoryReductionMB:F2} MB saved)");

        // Assert significant memory reduction (at least 40%)
        Assert.True(memoryReduction >= 40.0, 
            $"Expected at least 40% memory reduction, got {memoryReduction:F1}%");
    }

    [Fact]
    public async Task MemoryUsage_BatchProcessing_ShouldMaintainConstantMemoryFootprint()
    {
        // Arrange
        var batchSizes = new[] { 1000, 2000, 5000, 10000 };
        var memoryUsages = new List<(int BatchSize, long MemoryMB)>();

        _output.WriteLine($"=== BATCH PROCESSING MEMORY FOOTPRINT ANALYSIS ===");

        foreach (var batchSize in batchSizes)
        {
            // Clean up before each test
            _context.ChangeTracker.Clear();
            ForceGarbageCollection();

            var entities = GenerateTestEntities(batchSize);
            var store = new DepartmentPerformanceStore(_context, _logger, _mockPerformanceService.Object);

            // Measure memory usage
            var memoryBefore = GC.GetTotalMemory(false);
            var result = await store.BulkInsertAsync(entities, CancellationToken.None);
            var memoryAfter = GC.GetTotalMemory(false);

            Assert.True(result.Success);
            Assert.Equal(batchSize, result.ProcessedCount);

            var memoryUsedMB = (memoryAfter - memoryBefore) / 1024 / 1024;
            memoryUsages.Add((batchSize, memoryUsedMB));

            _output.WriteLine($"📦 Batch size: {batchSize:N0} | Memory used: {memoryUsedMB:F2} MB | Per entity: {(double)memoryUsedMB / batchSize * 1024:F2} KB");

            // Clean up entities for next iteration
            await _context.Database.EnsureDeletedAsync();
            await _context.Database.EnsureCreatedAsync();
        }

        // Analyze memory scaling
        var memoryPerEntityRatios = memoryUsages.Select(m => (double)m.MemoryMB / m.BatchSize * 1024).ToList(); // KB per entity
        var avgMemoryPerEntity = memoryPerEntityRatios.Average();
        var maxDeviation = memoryPerEntityRatios.Max() - memoryPerEntityRatios.Min();

        _output.WriteLine($"📈 Average memory per entity: {avgMemoryPerEntity:F2} KB");
        _output.WriteLine($"📊 Memory deviation range: {maxDeviation:F2} KB");

        // Assert memory usage scales linearly (deviation should be small)
        Assert.True(maxDeviation < avgMemoryPerEntity * 0.5, 
            $"Memory usage should scale linearly. Deviation {maxDeviation:F2} KB is too high compared to average {avgMemoryPerEntity:F2} KB");
    }

    [Fact]
    public async Task MemoryUsage_GarbageCollection_ShouldReleaseMemoryProperly()
    {
        // Arrange
        const int entityCount = 8000;
        var entities = GenerateTestEntities(entityCount);
        var store = new DepartmentPerformanceStore(_context, _logger, _mockPerformanceService.Object);

        _output.WriteLine($"=== GARBAGE COLLECTION ANALYSIS ===");
        _output.WriteLine($"Testing memory release with {entityCount:N0} entities");

        // Measure initial memory
        ForceGarbageCollection();
        var initialMemory = GC.GetTotalMemory(false);

        // Perform bulk operation
        var result = await store.BulkInsertAsync(entities, CancellationToken.None);
        Assert.True(result.Success);

        var memoryAfterOperation = GC.GetTotalMemory(false);
        var memoryUsedMB = (memoryAfterOperation - initialMemory) / 1024.0 / 1024.0;

        _output.WriteLine($"💾 Memory after operation: {memoryUsedMB:F2} MB");

        // Clear references and force garbage collection
        entities.Clear();
        _context.ChangeTracker.Clear();
        ForceGarbageCollection();

        var memoryAfterGC = GC.GetTotalMemory(false);
        var memoryReleasedMB = (memoryAfterOperation - memoryAfterGC) / 1024.0 / 1024.0;
        var releasePercentage = (memoryReleasedMB / memoryUsedMB) * 100;

        _output.WriteLine($"🗑️  Memory released by GC: {memoryReleasedMB:F2} MB ({releasePercentage:F1}%)");
        _output.WriteLine($"📉 Final memory: {(memoryAfterGC - initialMemory) / 1024.0 / 1024.0:F2} MB");

        // Assert significant memory release (at least 70%)
        Assert.True(releasePercentage >= 70.0, 
            $"Expected at least 70% memory release, got {releasePercentage:F1}%");
    }

    [Fact]
    public async Task MemoryUsage_ConcurrentOperations_ShouldNotCauseMemoryLeaks()
    {
        // Arrange
        const int operationsCount = 5;
        const int entitiesPerOperation = 2000;
        var memorySnapshots = new List<long>();

        _output.WriteLine($"=== CONCURRENT OPERATIONS MEMORY LEAK ANALYSIS ===");
        _output.WriteLine($"Running {operationsCount} concurrent operations with {entitiesPerOperation:N0} entities each");

        // Baseline memory
        ForceGarbageCollection();
        var baselineMemory = GC.GetTotalMemory(false);
        memorySnapshots.Add(baselineMemory);

        for (int i = 0; i < operationsCount; i++)
        {
            var entities = GenerateTestEntities(entitiesPerOperation);
            var store = new DepartmentPerformanceStore(_context, _logger, _mockPerformanceService.Object);

            // Run concurrent operations
            var tasks = new List<Task<BulkOperationResult>>
            {
                store.BulkInsertAsync(entities.Take(entitiesPerOperation / 2).ToList(), CancellationToken.None),
                store.BulkInsertAsync(entities.Skip(entitiesPerOperation / 2).ToList(), CancellationToken.None)
            };

            var results = await Task.WhenAll(tasks);
            Assert.All(results, r => Assert.True(r.Success));

            // Clean up and measure memory
            entities.Clear();
            _context.ChangeTracker.Clear();
            ForceGarbageCollection();

            var currentMemory = GC.GetTotalMemory(false);
            memorySnapshots.Add(currentMemory);

            var memoryIncreaseMB = (currentMemory - baselineMemory) / 1024.0 / 1024.0;
            _output.WriteLine($"🔄 Operation {i + 1}: Memory increase from baseline: {memoryIncreaseMB:F2} MB");

            // Clean up database for next iteration
            await _context.Database.EnsureDeletedAsync();
            await _context.Database.EnsureCreatedAsync();
        }

        // Analyze memory trend
        var finalMemory = memorySnapshots.Last();
        var totalMemoryIncrease = (finalMemory - baselineMemory) / 1024.0 / 1024.0;
        var averageIncreasePerOperation = totalMemoryIncrease / operationsCount;

        _output.WriteLine($"📊 Total memory increase: {totalMemoryIncrease:F2} MB");
        _output.WriteLine($"📈 Average increase per operation: {averageIncreasePerOperation:F2} MB");

        // Assert no significant memory leaks (less than 5MB increase per operation)
        Assert.True(averageIncreasePerOperation < 5.0, 
            $"Potential memory leak detected. Average increase {averageIncreasePerOperation:F2} MB per operation exceeds 5MB threshold");
    }

    [Fact]
    public async Task MemoryUsage_LongRunningOperations_ShouldMaintainStableMemory()
    {
        // Arrange
        const int iterations = 10;
        const int entitiesPerIteration = 1000;
        var memoryReadings = new List<long>();

        _output.WriteLine($"=== LONG-RUNNING OPERATIONS MEMORY STABILITY ANALYSIS ===");
        _output.WriteLine($"Running {iterations} iterations with {entitiesPerIteration:N0} entities each");

        var store = new DepartmentPerformanceStore(_context, _logger, _mockPerformanceService.Object);

        for (int i = 0; i < iterations; i++)
        {
            var entities = GenerateTestEntities(entitiesPerIteration);

            // Perform operation
            var result = await store.BulkInsertAsync(entities, CancellationToken.None);
            Assert.True(result.Success);

            // Clean up and measure memory
            entities.Clear();
            _context.ChangeTracker.Clear();
            ForceGarbageCollection();

            var currentMemory = GC.GetTotalMemory(false);
            memoryReadings.Add(currentMemory);

            _output.WriteLine($"🔄 Iteration {i + 1}: Memory: {currentMemory / 1024.0 / 1024.0:F2} MB");

            // Clean database for next iteration
            await _context.Database.EnsureDeletedAsync();
            await _context.Database.EnsureCreatedAsync();
        }

        // Analyze memory stability
        var avgMemory = memoryReadings.Average();
        var maxMemory = memoryReadings.Max();
        var minMemory = memoryReadings.Min();
        var memoryVariance = maxMemory - minMemory;
        var variancePercentage = (memoryVariance / avgMemory) * 100;

        _output.WriteLine($"📊 Average memory: {avgMemory / 1024.0 / 1024.0:F2} MB");
        _output.WriteLine($"📈 Max memory: {maxMemory / 1024.0 / 1024.0:F2} MB");
        _output.WriteLine($"📉 Min memory: {minMemory / 1024.0 / 1024.0:F2} MB");
        _output.WriteLine($"📊 Memory variance: {variancePercentage:F1}%");

        // Assert stable memory usage (variance should be less than 20%)
        Assert.True(variancePercentage < 20.0, 
            $"Memory usage is not stable. Variance {variancePercentage:F1}% exceeds 20% threshold");
    }

    #region Helper Methods

    private List<DepartmentPerformanceEntity> GenerateTestEntities(int count)
    {
        var entities = new List<DepartmentPerformanceEntity>(count);
        var random = new Random(42);

        for (int i = 0; i < count; i++)
        {
            entities.Add(new DepartmentPerformanceEntity
            {
                Id = Guid.NewGuid().ToString(),
                DepartmentId = $"dept_{i % 10}",
                AcademicYear = "2023-2024",
                Semester = i % 2 == 0 ? "Fall" : "Spring",
                PerformanceScore = random.Next(60, 100),
                StudentCount = random.Next(50, 200),
                CourseCount = random.Next(10, 50),
                AverageGPA = Math.Round(random.NextDouble() * 2 + 2, 2),
                CreatedAt = DateTime.UtcNow,
                IsActive = true
            });
        }

        return entities;
    }

    private async Task<long> MeasureMemoryWithoutOptimization(List<DepartmentPerformanceEntity> entities)
    {
        var memoryBefore = GC.GetTotalMemory(false);

        // Simulate old approach - no change tracker clearing
        _context.DepartmentPerformances.AddRange(entities);
        await _context.SaveChangesAsync();
        // Don't clear change tracker - this keeps all entities tracked

        var memoryAfter = GC.GetTotalMemory(false);
        return memoryAfter - memoryBefore;
    }

    private async Task<long> MeasureMemoryWithOptimization(List<DepartmentPerformanceEntity> entities)
    {
        var memoryBefore = GC.GetTotalMemory(false);

        // Use optimized approach with change tracker clearing
        var result = await _context.BulkInsertAsync(entities, performanceMonitor: _mockPerformanceService.Object);
        Assert.True(result.Success);

        var memoryAfter = GC.GetTotalMemory(false);
        return memoryAfter - memoryBefore;
    }

    private static void ForceGarbageCollection()
    {
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();
        GC.WaitForPendingFinalizers();
    }

    #endregion

    public void Dispose()
    {
        _context?.Dispose();
    }
}
