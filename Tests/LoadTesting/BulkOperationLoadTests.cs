using System.Diagnostics;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using Xunit.Abstractions;
using AcademicPerformance.DbContexts;
using AcademicPerformance.Extensions;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Services.Interfaces;
using AcademicPerformance.Stores;

namespace AcademicPerformance.Tests.LoadTesting;

/// <summary>
/// Load tests for bulk operations with large datasets
/// Tests system behavior under high-volume data processing scenarios
/// </summary>
public class BulkOperationLoadTests : IDisposable
{
    private readonly ITestOutputHelper _output;
    private readonly AcademicPerformanceDbContext _context;
    private readonly Mock<IPerformanceMonitoringService> _mockPerformanceService;
    private readonly ILogger<DepartmentPerformanceStore> _logger;

    public BulkOperationLoadTests(ITestOutputHelper output)
    {
        _output = output;

        // Setup in-memory database with larger memory allocation
        var options = new DbContextOptionsBuilder<AcademicPerformanceDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new AcademicPerformanceDbContext(options);
        _mockPerformanceService = new Mock<IPerformanceMonitoringService>();

        // Setup logger
        var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        _logger = loggerFactory.CreateLogger<DepartmentPerformanceStore>();
    }

    [Fact]
    public async Task BulkInsert_LargeDataset_10K_ShouldCompleteWithinTimeLimit()
    {
        // Arrange
        const int entityCount = 10_000;
        const int maxTimeSeconds = 30; // 30 seconds max for 10K entities

        var entities = GenerateLargeDepartmentDataset(entityCount);
        var store = new DepartmentPerformanceStore(_context, _logger, _mockPerformanceService.Object);

        _output.WriteLine($"=== LOAD TEST: {entityCount:N0} ENTITIES INSERT ===");
        _output.WriteLine($"Starting bulk insert of {entityCount:N0} entities...");

        // Act
        var stopwatch = Stopwatch.StartNew();
        var result = await store.BulkInsertAsync(entities, CancellationToken.None);
        stopwatch.Stop();

        // Assert
        Assert.True(result.Success, $"Bulk insert failed: {result.ErrorMessage}");
        Assert.Equal(entityCount, result.ProcessedCount);
        Assert.True(stopwatch.Elapsed.TotalSeconds < maxTimeSeconds, 
            $"Insert took {stopwatch.Elapsed.TotalSeconds:F2}s, expected less than {maxTimeSeconds}s");

        // Performance metrics
        var throughput = entityCount / stopwatch.Elapsed.TotalSeconds;
        _output.WriteLine($"✅ Insert completed successfully");
        _output.WriteLine($"⏱️  Time: {stopwatch.Elapsed.TotalSeconds:F2} seconds");
        _output.WriteLine($"🚀 Throughput: {throughput:F0} entities/second");
        _output.WriteLine($"💾 Memory usage: {GC.GetTotalMemory(false) / 1024 / 1024:F2} MB");

        // Verify data integrity
        var insertedCount = await _context.DepartmentPerformances.CountAsync();
        Assert.Equal(entityCount, insertedCount);

        // Verify performance monitoring was called
        _mockPerformanceService.Verify(x => x.ReportBatchProgressAsync(
            It.IsAny<string>(), It.IsAny<int>(), It.IsAny<int>()), Times.AtLeastOnce);
    }

    [Fact]
    public async Task BulkUpdate_LargeDataset_5K_ShouldCompleteWithinTimeLimit()
    {
        // Arrange
        const int entityCount = 5_000;
        const int maxTimeSeconds = 20; // 20 seconds max for 5K updates

        var entities = GenerateLargeDepartmentDataset(entityCount);
        var store = new DepartmentPerformanceStore(_context, _logger, _mockPerformanceService.Object);

        // Insert entities first
        var insertResult = await store.BulkInsertAsync(entities, CancellationToken.None);
        Assert.True(insertResult.Success);

        // Modify entities for update
        var insertedEntities = await _context.DepartmentPerformances.ToListAsync();
        foreach (var entity in insertedEntities)
        {
            entity.PerformanceScore += 10;
            entity.AverageGPA = Math.Min(4.0, entity.AverageGPA + 0.1);
        }

        _output.WriteLine($"=== LOAD TEST: {entityCount:N0} ENTITIES UPDATE ===");
        _output.WriteLine($"Starting bulk update of {entityCount:N0} entities...");

        // Act
        var stopwatch = Stopwatch.StartNew();
        var result = await store.BulkUpdateAsync(insertedEntities, CancellationToken.None);
        stopwatch.Stop();

        // Assert
        Assert.True(result.Success, $"Bulk update failed: {result.ErrorMessage}");
        Assert.Equal(entityCount, result.ProcessedCount);
        Assert.True(stopwatch.Elapsed.TotalSeconds < maxTimeSeconds, 
            $"Update took {stopwatch.Elapsed.TotalSeconds:F2}s, expected less than {maxTimeSeconds}s");

        // Performance metrics
        var throughput = entityCount / stopwatch.Elapsed.TotalSeconds;
        _output.WriteLine($"✅ Update completed successfully");
        _output.WriteLine($"⏱️  Time: {stopwatch.Elapsed.TotalSeconds:F2} seconds");
        _output.WriteLine($"🚀 Throughput: {throughput:F0} entities/second");
    }

    [Fact]
    public async Task BulkOperations_StressTest_MultipleStores_ShouldHandleConcurrentLoad()
    {
        // Arrange
        const int entitiesPerStore = 2_000;
        const int maxTimeSeconds = 45; // 45 seconds for concurrent operations

        var departmentStore = new DepartmentPerformanceStore(_context, _logger, _mockPerformanceService.Object);
        var academicianStore = new AcademicianStore(_context, 
            LoggerFactory.Create(b => b.AddConsole()).CreateLogger<AcademicianStore>(), 
            _mockPerformanceService.Object);
        var staffStore = new StaffCompetencyStore(_context, 
            LoggerFactory.Create(b => b.AddConsole()).CreateLogger<StaffCompetencyStore>(), 
            _mockPerformanceService.Object);

        var departmentEntities = GenerateLargeDepartmentDataset(entitiesPerStore);
        var academicianEntities = GenerateLargeAcademicianDataset(entitiesPerStore);
        var staffEntities = GenerateLargeStaffCompetencyDataset(entitiesPerStore);

        _output.WriteLine($"=== STRESS TEST: {entitiesPerStore:N0} ENTITIES PER STORE ===");
        _output.WriteLine($"Testing concurrent operations across multiple stores...");

        // Act
        var stopwatch = Stopwatch.StartNew();
        var tasks = new List<Task<BulkOperationResult>>
        {
            departmentStore.BulkInsertAsync(departmentEntities, CancellationToken.None),
            academicianStore.CreateAcademicianProfilesAsync(academicianEntities, CancellationToken.None),
            staffStore.BulkInsertAsync(staffEntities, CancellationToken.None)
        };

        var results = await Task.WhenAll(tasks);
        stopwatch.Stop();

        // Assert
        Assert.All(results, result => 
        {
            Assert.True(result.Success, $"Operation failed: {result.ErrorMessage}");
            Assert.Equal(entitiesPerStore, result.ProcessedCount);
        });

        Assert.True(stopwatch.Elapsed.TotalSeconds < maxTimeSeconds, 
            $"Concurrent operations took {stopwatch.Elapsed.TotalSeconds:F2}s, expected less than {maxTimeSeconds}s");

        // Performance metrics
        var totalEntities = entitiesPerStore * 3;
        var throughput = totalEntities / stopwatch.Elapsed.TotalSeconds;
        _output.WriteLine($"✅ All concurrent operations completed successfully");
        _output.WriteLine($"⏱️  Total time: {stopwatch.Elapsed.TotalSeconds:F2} seconds");
        _output.WriteLine($"🚀 Combined throughput: {throughput:F0} entities/second");
        _output.WriteLine($"📊 Total entities processed: {totalEntities:N0}");

        // Verify data integrity
        var departmentCount = await _context.DepartmentPerformances.CountAsync();
        var academicianCount = await _context.Academicians.CountAsync();
        var staffCount = await _context.StaffCompetencyEvaluations.CountAsync();

        Assert.Equal(entitiesPerStore, departmentCount);
        Assert.Equal(entitiesPerStore, academicianCount);
        Assert.Equal(entitiesPerStore, staffCount);
    }

    [Fact]
    public async Task BulkOperations_MemoryStressTest_ShouldNotExceedMemoryLimits()
    {
        // Arrange
        const int entityCount = 15_000;
        const long maxMemoryMB = 500; // 500 MB limit

        var entities = GenerateLargeDepartmentDataset(entityCount);
        var store = new DepartmentPerformanceStore(_context, _logger, _mockPerformanceService.Object);

        _output.WriteLine($"=== MEMORY STRESS TEST: {entityCount:N0} ENTITIES ===");

        // Measure initial memory
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();
        var initialMemory = GC.GetTotalMemory(false);

        // Act
        var result = await store.BulkInsertAsync(entities, CancellationToken.None);

        // Measure final memory
        var finalMemory = GC.GetTotalMemory(false);
        var memoryUsedMB = (finalMemory - initialMemory) / 1024 / 1024;

        // Assert
        Assert.True(result.Success);
        Assert.Equal(entityCount, result.ProcessedCount);
        Assert.True(memoryUsedMB < maxMemoryMB, 
            $"Memory usage {memoryUsedMB}MB exceeded limit of {maxMemoryMB}MB");

        _output.WriteLine($"✅ Memory stress test completed");
        _output.WriteLine($"💾 Memory used: {memoryUsedMB:F2} MB (limit: {maxMemoryMB} MB)");
        _output.WriteLine($"📈 Memory efficiency: {entityCount / memoryUsedMB:F0} entities/MB");

        // Force garbage collection and verify memory is released
        entities.Clear();
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();

        var afterGCMemory = GC.GetTotalMemory(false);
        var memoryReleased = (finalMemory - afterGCMemory) / 1024 / 1024;
        _output.WriteLine($"🗑️  Memory released after GC: {memoryReleased:F2} MB");
    }

    [Fact]
    public async Task BulkOperations_BatchSizeOptimization_ShouldProcessEfficientlyInBatches()
    {
        // Arrange
        const int entityCount = 8_000;
        var entities = GenerateLargeDepartmentDataset(entityCount);
        var store = new DepartmentPerformanceStore(_context, _logger, _mockPerformanceService.Object);

        _output.WriteLine($"=== BATCH SIZE OPTIMIZATION TEST: {entityCount:N0} ENTITIES ===");

        // Act
        var stopwatch = Stopwatch.StartNew();
        var result = await store.BulkInsertAsync(entities, CancellationToken.None);
        stopwatch.Stop();

        // Assert
        Assert.True(result.Success);
        Assert.Equal(entityCount, result.ProcessedCount);

        // Verify batch processing was used (should see multiple batch progress reports)
        _mockPerformanceService.Verify(x => x.ReportBatchProgressAsync(
            It.IsAny<string>(), It.IsAny<int>(), It.IsAny<int>()), Times.AtLeast(2));

        // Performance should be consistent regardless of dataset size
        var throughput = entityCount / stopwatch.Elapsed.TotalSeconds;
        Assert.True(throughput > 1000, $"Throughput {throughput:F0} entities/second is too low");

        _output.WriteLine($"✅ Batch processing test completed");
        _output.WriteLine($"⏱️  Time: {stopwatch.Elapsed.TotalSeconds:F2} seconds");
        _output.WriteLine($"🚀 Throughput: {throughput:F0} entities/second");
        _output.WriteLine($"📦 Batch processing verified through monitoring calls");
    }

    #region Helper Methods

    private List<DepartmentPerformanceEntity> GenerateLargeDepartmentDataset(int count)
    {
        var entities = new List<DepartmentPerformanceEntity>(count);
        var random = new Random(42); // Fixed seed for consistent results
        var departments = new[] { "CS", "EE", "ME", "CE", "IE", "MATH", "PHYS", "CHEM", "BIO", "ECON" };
        var semesters = new[] { "Fall", "Spring", "Summer" };
        var years = new[] { "2021-2022", "2022-2023", "2023-2024", "2024-2025" };

        for (int i = 0; i < count; i++)
        {
            entities.Add(new DepartmentPerformanceEntity
            {
                Id = Guid.NewGuid().ToString(),
                DepartmentId = departments[i % departments.Length],
                AcademicYear = years[i % years.Length],
                Semester = semesters[i % semesters.Length],
                PerformanceScore = random.Next(60, 100),
                StudentCount = random.Next(50, 300),
                CourseCount = random.Next(10, 80),
                AverageGPA = Math.Round(random.NextDouble() * 2 + 2, 2), // 2.0 - 4.0
                CreatedAt = DateTime.UtcNow.AddDays(-random.Next(1, 365)),
                IsActive = true
            });
        }

        return entities;
    }

    private List<AcademicianEntity> GenerateLargeAcademicianDataset(int count)
    {
        var entities = new List<AcademicianEntity>(count);
        var random = new Random(42);
        var departments = new[] { "CS", "EE", "ME", "CE", "IE", "MATH", "PHYS", "CHEM", "BIO", "ECON" };
        var titles = new[] { "Professor", "Associate Professor", "Assistant Professor", "Lecturer" };
        var firstNames = new[] { "John", "Jane", "Michael", "Sarah", "David", "Lisa", "Robert", "Emily" };
        var lastNames = new[] { "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis" };

        for (int i = 0; i < count; i++)
        {
            var firstName = firstNames[i % firstNames.Length];
            var lastName = lastNames[i % lastNames.Length];
            
            entities.Add(new AcademicianEntity
            {
                Id = Guid.NewGuid().ToString(),
                FirstName = firstName,
                LastName = lastName,
                Email = $"{firstName.ToLower()}.{lastName.ToLower()}{i}@university.edu",
                DepartmentId = departments[i % departments.Length],
                Title = titles[i % titles.Length],
                IsActive = true
            });
        }

        return entities;
    }

    private List<StaffCompetencyEvaluationEntity> GenerateLargeStaffCompetencyDataset(int count)
    {
        var entities = new List<StaffCompetencyEvaluationEntity>(count);
        var random = new Random(42);
        var statuses = new[] { "Active", "Pending", "Reviewed", "Approved" };

        for (int i = 0; i < count; i++)
        {
            entities.Add(new StaffCompetencyEvaluationEntity
            {
                Id = Guid.NewGuid().ToString(),
                StaffId = $"staff_{i % 100}", // 100 different staff members
                CompetencyId = $"comp_{i % 50}", // 50 different competencies
                EvaluationScore = random.Next(60, 100),
                Status = statuses[i % statuses.Length],
                EvaluationDate = DateTime.UtcNow.AddDays(-random.Next(1, 365)),
                IsActive = true
            });
        }

        return entities;
    }

    #endregion

    public void Dispose()
    {
        _context?.Dispose();
    }
}
