using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using AcademicPerformance.DbContexts;
using AcademicPerformance.Extensions;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Services.Interfaces;
using AcademicPerformance.Stores;

namespace AcademicPerformance.Tests.Integration;

/// <summary>
/// Integration tests for all refactored store bulk operations
/// Tests the complete workflow from store methods through BulkOperationExtensions
/// </summary>
public class BulkOperationIntegrationTests : IDisposable
{
    private readonly AcademicPerformanceDbContext _context;
    private readonly Mock<IPerformanceMonitoringService> _mockPerformanceService;
    private readonly IServiceProvider _serviceProvider;

    public BulkOperationIntegrationTests()
    {
        // Setup in-memory database
        var options = new DbContextOptionsBuilder<AcademicPerformanceDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new AcademicPerformanceDbContext(options);
        _mockPerformanceService = new Mock<IPerformanceMonitoringService>();

        // Setup service provider for DI
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole());
        services.AddSingleton(_mockPerformanceService.Object);
        _serviceProvider = services.BuildServiceProvider();
    }

    [Fact]
    public async Task DepartmentPerformanceStore_BulkOperations_ShouldWorkEndToEnd()
    {
        // Arrange
        var logger = _serviceProvider.GetRequiredService<ILogger<DepartmentPerformanceStore>>();
        var store = new DepartmentPerformanceStore(_context, logger, _mockPerformanceService.Object);

        var entities = new List<DepartmentPerformanceEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                DepartmentId = "CS",
                AcademicYear = "2023-2024",
                Semester = "Fall",
                PerformanceScore = 85,
                StudentCount = 150,
                CourseCount = 25,
                AverageGPA = 3.2,
                IsActive = true
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                DepartmentId = "EE",
                AcademicYear = "2023-2024",
                Semester = "Fall",
                PerformanceScore = 90,
                StudentCount = 120,
                CourseCount = 20,
                AverageGPA = 3.5,
                IsActive = true
            }
        };

        // Act & Assert - Insert
        var insertResult = await store.BulkInsertAsync(entities, CancellationToken.None);
        Assert.True(insertResult.Success);
        Assert.Equal(2, insertResult.ProcessedCount);

        // Verify entities were inserted with business logic applied
        var insertedEntities = await _context.DepartmentPerformances.ToListAsync();
        Assert.Equal(2, insertedEntities.Count);
        Assert.All(insertedEntities, entity => 
        {
            Assert.NotNull(entity.CreatedAt);
            Assert.NotNull(entity.UpdatedAt);
            Assert.True(entity.IsActive);
            Assert.InRange(entity.PerformanceGrade, 'A', 'F');
        });

        // Act & Assert - Update
        foreach (var entity in insertedEntities)
        {
            entity.PerformanceScore += 5;
        }

        var updateResult = await store.BulkUpdateAsync(insertedEntities, CancellationToken.None);
        Assert.True(updateResult.Success);
        Assert.Equal(2, updateResult.ProcessedCount);

        // Verify performance monitoring was called
        _mockPerformanceService.Verify(x => x.ReportBatchProgressAsync(
            It.IsAny<string>(), It.IsAny<int>(), It.IsAny<int>()), Times.AtLeastOnce);
    }

    [Fact]
    public async Task AcademicianStore_BulkOperations_ShouldWorkEndToEnd()
    {
        // Arrange
        var logger = _serviceProvider.GetRequiredService<ILogger<AcademicianStore>>();
        var store = new AcademicianStore(_context, logger, _mockPerformanceService.Object);

        var entities = new List<AcademicianEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                DepartmentId = "CS",
                Title = "Professor",
                IsActive = true
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                FirstName = "Jane",
                LastName = "Smith",
                Email = "<EMAIL>",
                DepartmentId = "EE",
                Title = "Associate Professor",
                IsActive = true
            }
        };

        // Act & Assert - Create Profiles
        var createResult = await store.CreateAcademicianProfilesAsync(entities, CancellationToken.None);
        Assert.True(createResult.Success);
        Assert.Equal(2, createResult.ProcessedCount);

        // Verify entities were created with business logic applied
        var createdEntities = await _context.Academicians.ToListAsync();
        Assert.Equal(2, createdEntities.Count);
        Assert.All(createdEntities, entity => 
        {
            Assert.NotNull(entity.FullName);
            Assert.Contains(" ", entity.FullName); // Should be "FirstName LastName"
            Assert.NotNull(entity.CreatedAt);
            Assert.NotNull(entity.UpdatedAt);
        });

        // Act & Assert - Update Profiles
        foreach (var entity in createdEntities)
        {
            entity.Title = "Full Professor";
        }

        var updateResult = await store.UpdateAcademicianProfilesAsync(createdEntities, CancellationToken.None);
        Assert.True(updateResult.Success);
        Assert.Equal(2, updateResult.ProcessedCount);

        // Act & Assert - Update Last Synced
        var syncResult = await store.UpdateLastSyncedAtBulkAsync(
            createdEntities.Select(e => e.Id).ToList(), CancellationToken.None);
        Assert.True(syncResult.Success);
        Assert.Equal(2, syncResult.ProcessedCount);

        // Verify LastSyncedAt was updated
        var syncedEntities = await _context.Academicians.ToListAsync();
        Assert.All(syncedEntities, entity => Assert.NotNull(entity.LastSyncedAt));
    }

    [Fact]
    public async Task StaffCompetencyStore_BulkOperations_ShouldWorkEndToEnd()
    {
        // Arrange
        var logger = _serviceProvider.GetRequiredService<ILogger<StaffCompetencyStore>>();
        var store = new StaffCompetencyStore(_context, logger, _mockPerformanceService.Object);

        var entities = new List<StaffCompetencyEvaluationEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                StaffId = "staff1",
                CompetencyId = "comp1",
                EvaluationScore = 85,
                Status = "Active",
                EvaluationDate = DateTime.UtcNow,
                IsActive = true
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                StaffId = "staff2",
                CompetencyId = "comp2",
                EvaluationScore = 90,
                Status = "Active",
                EvaluationDate = DateTime.UtcNow,
                IsActive = true
            }
        };

        // Act & Assert - Insert
        var insertResult = await store.BulkInsertAsync(entities, CancellationToken.None);
        Assert.True(insertResult.Success);
        Assert.Equal(2, insertResult.ProcessedCount);

        // Act & Assert - Update Status
        var statusUpdateResult = await store.BulkUpdateStatusAsync(
            entities.Select(e => e.Id).ToList(), "Reviewed", CancellationToken.None);
        Assert.True(statusUpdateResult.Success);
        Assert.Equal(2, statusUpdateResult.ProcessedCount);

        // Verify status was updated
        var updatedEntities = await _context.StaffCompetencyEvaluations.ToListAsync();
        Assert.All(updatedEntities, entity => Assert.Equal("Reviewed", entity.Status));

        // Act & Assert - Archive Old Evaluations
        var archiveResult = await store.ArchiveOldEvaluationsAsync(365, CancellationToken.None);
        Assert.True(archiveResult.Success);
        // Should be 0 since our test entities are new
        Assert.Equal(0, archiveResult.ProcessedCount);
    }

    [Fact]
    public async Task PortfolioControlStore_BulkOperations_ShouldWorkEndToEnd()
    {
        // Arrange
        var logger = _serviceProvider.GetRequiredService<ILogger<PortfolioControlStore>>();
        var store = new PortfolioControlStore(_context, logger, _mockPerformanceService.Object);

        var entities = new List<CoursePortfolioVerificationEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CourseId = "course1",
                StudentId = "student1",
                VerificationStatus = "Pending",
                Priority = "Medium",
                Score = 85,
                Deleted = false
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CourseId = "course2",
                StudentId = "student2",
                VerificationStatus = "Pending",
                Priority = "High",
                Score = 90,
                Deleted = false
            }
        };

        // Act & Assert - Insert
        var insertResult = await store.BulkInsertAsync(entities, CancellationToken.None);
        Assert.True(insertResult.Success);
        Assert.Equal(2, insertResult.ProcessedCount);

        // Act & Assert - Update Course Verifications
        var insertedEntities = await _context.CoursePortfolioVerifications.ToListAsync();
        foreach (var entity in insertedEntities)
        {
            entity.VerificationStatus = "Approved";
        }

        var updateResult = await store.BulkUpdateCourseVerificationsAsync(insertedEntities, CancellationToken.None);
        Assert.True(updateResult.Success);
        Assert.Equal(2, updateResult.ProcessedCount);

        // Verify business logic was applied
        var updatedEntities = await _context.CoursePortfolioVerifications.ToListAsync();
        Assert.All(updatedEntities, entity => 
        {
            Assert.Equal("Approved", entity.VerificationStatus);
            Assert.NotNull(entity.VerifiedAt); // Should be set by business logic
        });
    }

    [Fact]
    public async Task FeedbackStore_BulkOperations_ShouldWorkEndToEnd()
    {
        // Arrange
        var logger = _serviceProvider.GetRequiredService<ILogger<FeedbackStore>>();
        var store = new FeedbackStore(_context, logger, _mockPerformanceService.Object);

        var feedbackEntities = new List<SubmissionFeedbackEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                SubmissionId = "submission1",
                FeedbackType = "General",
                Score = 85,
                Status = "Draft",
                IsActive = true
            }
        };

        var criterionEntities = new List<CriterionFeedbackEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CriterionId = "criterion1",
                SubmissionId = "submission1",
                Score = 90,
                Status = "Draft",
                FeedbackType = "Criterion",
                IsActive = true
            }
        };

        // Act & Assert - Create Bulk Feedback
        var feedbackResult = await store.CreateBulkFeedbackAsync(feedbackEntities, CancellationToken.None);
        Assert.True(feedbackResult.Success);
        Assert.Equal(1, feedbackResult.ProcessedCount);

        // Act & Assert - Create Bulk Criterion Feedback
        var criterionResult = await store.CreateBulkCriterionFeedbackAsync(criterionEntities, CancellationToken.None);
        Assert.True(criterionResult.Success);
        Assert.Equal(1, criterionResult.ProcessedCount);

        // Act & Assert - Update Feedback
        var insertedFeedback = await _context.SubmissionFeedbacks.ToListAsync();
        foreach (var entity in insertedFeedback)
        {
            entity.Status = "Approved";
        }

        var updateResult = await store.BulkUpdateAsync(insertedFeedback, CancellationToken.None);
        Assert.True(updateResult.Success);
        Assert.Equal(1, updateResult.ProcessedCount);

        // Verify all entities have proper audit fields
        var allFeedback = await _context.SubmissionFeedbacks.ToListAsync();
        var allCriterion = await _context.CriterionFeedbacks.ToListAsync();

        Assert.All(allFeedback, entity => 
        {
            Assert.NotNull(entity.CreatedAt);
            Assert.NotNull(entity.UpdatedAt);
            Assert.True(entity.IsActive);
        });

        Assert.All(allCriterion, entity => 
        {
            Assert.NotNull(entity.CreatedAt);
            Assert.NotNull(entity.UpdatedAt);
            Assert.True(entity.IsActive);
        });
    }

    [Fact]
    public async Task AllStores_ConcurrentOperations_ShouldHandleCorrectly()
    {
        // Arrange - Create multiple stores
        var departmentStore = new DepartmentPerformanceStore(
            _context, 
            _serviceProvider.GetRequiredService<ILogger<DepartmentPerformanceStore>>(), 
            _mockPerformanceService.Object);

        var academicianStore = new AcademicianStore(
            _context, 
            _serviceProvider.GetRequiredService<ILogger<AcademicianStore>>(), 
            _mockPerformanceService.Object);

        // Create test data
        var departmentEntities = new List<DepartmentPerformanceEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                DepartmentId = "CS",
                AcademicYear = "2023-2024",
                Semester = "Fall",
                PerformanceScore = 85,
                StudentCount = 150,
                CourseCount = 25,
                AverageGPA = 3.2,
                IsActive = true
            }
        };

        var academicianEntities = new List<AcademicianEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                DepartmentId = "CS",
                Title = "Professor",
                IsActive = true
            }
        };

        // Act - Run concurrent operations
        var tasks = new List<Task<BulkOperationResult>>
        {
            departmentStore.BulkInsertAsync(departmentEntities, CancellationToken.None),
            academicianStore.CreateAcademicianProfilesAsync(academicianEntities, CancellationToken.None)
        };

        var results = await Task.WhenAll(tasks);

        // Assert - All operations should succeed
        Assert.All(results, result => 
        {
            Assert.True(result.Success);
            Assert.Equal(1, result.ProcessedCount);
        });

        // Verify data integrity
        var departmentCount = await _context.DepartmentPerformances.CountAsync();
        var academicianCount = await _context.Academicians.CountAsync();

        Assert.Equal(1, departmentCount);
        Assert.Equal(1, academicianCount);
    }

    public void Dispose()
    {
        _context?.Dispose();
        _serviceProvider?.GetService<IServiceScope>()?.Dispose();
    }
}
