using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using AcademicPerformance.DbContexts;
using AcademicPerformance.Extensions;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Services.Interfaces;
using AcademicPerformance.Stores;

namespace AcademicPerformance.Tests.Stores;

/// <summary>
/// Unit tests for PortfolioControlStore refactored methods
/// </summary>
public class PortfolioControlStoreTests : IDisposable
{
    private readonly AcademicPerformanceDbContext _context;
    private readonly Mock<ILogger<PortfolioControlStore>> _mockLogger;
    private readonly Mock<IPerformanceMonitoringService> _mockPerformanceService;
    private readonly PortfolioControlStore _store;

    public PortfolioControlStoreTests()
    {
        // Setup in-memory database
        var options = new DbContextOptionsBuilder<AcademicPerformanceDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new AcademicPerformanceDbContext(options);
        _mockLogger = new Mock<ILogger<PortfolioControlStore>>();
        _mockPerformanceService = new Mock<IPerformanceMonitoringService>();

        _store = new PortfolioControlStore(_context, _mockLogger.Object, _mockPerformanceService.Object);
    }

    [Fact]
    public async Task BulkUpdateCourseVerificationsAsync_WithValidEntities_ShouldUpdateSuccessfully()
    {
        // Arrange
        var entities = new List<CoursePortfolioVerificationEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CourseId = "course1",
                StudentId = "student1",
                VerificationStatus = "Pending",
                Priority = "Medium",
                Score = 85,
                Deleted = false
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CourseId = "course2",
                StudentId = "student2",
                VerificationStatus = "Pending",
                Priority = "High",
                Score = 90,
                Deleted = false
            }
        };

        await _context.CoursePortfolioVerifications.AddRangeAsync(entities);
        await _context.SaveChangesAsync();

        // Update entities
        foreach (var entity in entities)
        {
            entity.VerificationStatus = "Approved";
        }

        // Act
        var result = await _store.BulkUpdateCourseVerificationsAsync(entities, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(2, result.ProcessedCount);
        Assert.True(result.ElapsedTime > TimeSpan.Zero);

        // Verify entities were updated with business logic applied
        var updatedEntities = await _context.CoursePortfolioVerifications.ToListAsync();
        Assert.All(updatedEntities, entity => 
        {
            Assert.Equal("Approved", entity.VerificationStatus);
            Assert.NotNull(entity.VerifiedAt); // Should be set by business logic
            Assert.NotNull(entity.UpdatedAt);
        });

        // Verify performance monitoring was called
        _mockPerformanceService.Verify(x => x.ReportBatchProgressAsync(
            It.IsAny<string>(), It.IsAny<int>(), It.IsAny<int>()), Times.AtLeastOnce);
    }

    [Fact]
    public async Task BulkUpdateCourseVerificationsAsync_WithInvalidEntities_ShouldReturnValidationErrors()
    {
        // Arrange
        var entities = new List<CoursePortfolioVerificationEntity>
        {
            new()
            {
                Id = "", // Invalid - empty ID
                CourseId = "course1",
                StudentId = "student1",
                VerificationStatus = "Pending",
                Deleted = false
            }
        };

        // Act
        var result = await _store.BulkUpdateCourseVerificationsAsync(entities, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Equal(0, result.ProcessedCount);
        Assert.Contains("Update için entity Id boş olamaz", result.ErrorMessage);
    }

    [Fact]
    public async Task BulkInsertAsync_WithValidEntities_ShouldInsertSuccessfully()
    {
        // Arrange
        var entities = new List<CoursePortfolioVerificationEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CourseId = "course1",
                StudentId = "student1",
                VerificationStatus = "Pending",
                Priority = "Medium",
                Score = 85
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CourseId = "course2",
                StudentId = "student2",
                VerificationStatus = "InReview",
                Priority = "High",
                Score = 90
            }
        };

        // Act
        var result = await _store.BulkInsertAsync(entities, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(2, result.ProcessedCount);

        // Verify entities were inserted with business logic applied
        var insertedEntities = await _context.CoursePortfolioVerifications.ToListAsync();
        Assert.Equal(2, insertedEntities.Count);
        Assert.All(insertedEntities, entity => 
        {
            Assert.NotNull(entity.CreatedAt);
            Assert.NotNull(entity.UpdatedAt);
            Assert.Contains(entity.Priority, new[] { "Low", "Medium", "High", "Critical" });
        });
    }

    [Fact]
    public async Task BulkInsertAsync_WithInvalidEntities_ShouldReturnValidationErrors()
    {
        // Arrange
        var entities = new List<CoursePortfolioVerificationEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CourseId = "", // Invalid - empty
                StudentId = "student1",
                VerificationStatus = "InvalidStatus", // Invalid status
                Score = 85
            }
        };

        // Act
        var result = await _store.BulkInsertAsync(entities, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Equal(0, result.ProcessedCount);
        Assert.Contains("CourseId boş olamaz", result.ErrorMessage);
        Assert.Contains("Geçersiz verification status", result.ErrorMessage);
    }

    [Fact]
    public async Task BulkDeleteAsync_WithValidEntities_ShouldDeleteSuccessfully()
    {
        // Arrange
        var entities = new List<CoursePortfolioVerificationEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CourseId = "course1",
                StudentId = "student1",
                VerificationStatus = "Pending",
                Priority = "Medium"
            }
        };

        await _context.CoursePortfolioVerifications.AddRangeAsync(entities);
        await _context.SaveChangesAsync();

        // Act
        var result = await _store.BulkDeleteAsync(entities, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(1, result.ProcessedCount);

        // Verify entity was deleted
        var remainingEntities = await _context.CoursePortfolioVerifications.ToListAsync();
        Assert.Empty(remainingEntities);
    }

    [Fact]
    public async Task BulkDeleteAsync_WithApprovedEntity_ShouldReturnValidationErrors()
    {
        // Arrange
        var entities = new List<CoursePortfolioVerificationEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CourseId = "course1",
                StudentId = "student1",
                VerificationStatus = "Approved", // Cannot delete approved entities
                Priority = "Medium"
            }
        };

        // Act
        var result = await _store.BulkDeleteAsync(entities, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Equal(0, result.ProcessedCount);
        Assert.Contains("Approved verification silinemez", result.ErrorMessage);
    }

    [Fact]
    public async Task BulkUpdateCourseVerificationsAsync_WithEmptyList_ShouldReturnZeroProcessed()
    {
        // Arrange
        var emptyList = new List<CoursePortfolioVerificationEntity>();

        // Act
        var result = await _store.BulkUpdateCourseVerificationsAsync(emptyList, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(0, result.ProcessedCount);
    }

    [Fact]
    public async Task BulkOperations_ShouldLogAppropriateMessages()
    {
        // Arrange
        var entities = new List<CoursePortfolioVerificationEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CourseId = "course1",
                StudentId = "student1",
                VerificationStatus = "Pending",
                Priority = "Medium"
            }
        };

        // Act
        await _store.BulkInsertAsync(entities, CancellationToken.None);

        // Assert - Verify logging calls
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("bulk insert başlatılıyor")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);

        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("bulk insert tamamlandı")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task BusinessLogic_ShouldApplyCorrectly()
    {
        // Arrange
        var entity = new CoursePortfolioVerificationEntity
        {
            Id = Guid.NewGuid().ToString(),
            CourseId = "course1",
            StudentId = "student1",
            VerificationStatus = "Approved",
            Priority = "InvalidPriority", // Should be corrected to "Medium"
            Score = 150 // Should be corrected to 100
        };

        var entities = new List<CoursePortfolioVerificationEntity> { entity };

        // Act
        var result = await _store.BulkInsertAsync(entities, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        
        var insertedEntity = await _context.CoursePortfolioVerifications.FirstAsync();
        Assert.Equal("Medium", insertedEntity.Priority); // Corrected by business logic
        Assert.Equal(100, insertedEntity.Score); // Corrected by business logic
        Assert.NotNull(insertedEntity.VerifiedAt); // Set by business logic for approved status
    }

    public void Dispose()
    {
        _context?.Dispose();
    }
}
