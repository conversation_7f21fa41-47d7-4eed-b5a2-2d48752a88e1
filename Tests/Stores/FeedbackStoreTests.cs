using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using AcademicPerformance.DbContexts;
using AcademicPerformance.Extensions;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;

using AcademicPerformance.Stores;

namespace AcademicPerformance.Tests.Stores;

/// <summary>
/// Unit tests for FeedbackStore refactored methods
/// </summary>
public class FeedbackStoreTests : IDisposable
{
    private readonly AcademicPerformanceDbContext _context;
    private readonly Mock<ILogger<FeedbackStore>> _mockLogger;

    private readonly FeedbackStore _store;

    public FeedbackStoreTests()
    {
        // Setup in-memory database
        var options = new DbContextOptionsBuilder<AcademicPerformanceDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new AcademicPerformanceDbContext(options);
        _mockLogger = new Mock<ILogger<FeedbackStore>>();
        _mockPerformanceService = new Mock<IPerformanceMonitoringService>();

        _store = new FeedbackStore(_context, _mockLogger.Object, _mockPerformanceService.Object);
    }

    [Fact]
    public async Task CreateBulkFeedbackAsync_WithValidEntities_ShouldInsertSuccessfully()
    {
        // Arrange
        var entities = new List<SubmissionFeedbackEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                SubmissionId = "submission1",
                FeedbackType = "General",
                Score = 85,
                Status = "Draft",
                IsActive = true
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                SubmissionId = "submission2",
                FeedbackType = "Detailed",
                Score = 90,
                Status = "Submitted",
                IsActive = true
            }
        };

        // Act
        var result = await _store.CreateBulkFeedbackAsync(entities, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(2, result.ProcessedCount);
        Assert.True(result.ElapsedTime > TimeSpan.Zero);

        // Verify entities were inserted with business logic applied
        var insertedEntities = await _context.SubmissionFeedbacks.ToListAsync();
        Assert.Equal(2, insertedEntities.Count);
        Assert.All(insertedEntities, entity =>
        {
            Assert.NotNull(entity.CreatedAt);
            Assert.NotNull(entity.UpdatedAt);
            Assert.True(entity.IsActive);
            Assert.Contains(entity.FeedbackType, new[] { "General", "Detailed" });
        });

        // Verify performance monitoring was called
        _mockPerformanceService.Verify(x => x.ReportBatchProgressAsync(
            It.IsAny<string>(), It.IsAny<int>(), It.IsAny<int>()), Times.AtLeastOnce);
    }

    [Fact]
    public async Task CreateBulkFeedbackAsync_WithInvalidEntities_ShouldReturnValidationErrors()
    {
        // Arrange
        var entities = new List<SubmissionFeedbackEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                SubmissionId = "", // Invalid - empty
                FeedbackType = "General",
                Score = 150, // Invalid - out of range
                Status = "Draft"
            }
        };

        // Act
        var result = await _store.CreateBulkFeedbackAsync(entities, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Equal(0, result.ProcessedCount);
        Assert.Contains("SubmissionId boş olamaz", result.ErrorMessage);
        Assert.Contains("Score 0-100 arasında olmalı", result.ErrorMessage);
    }

    [Fact]
    public async Task CreateBulkCriterionFeedbackAsync_WithValidEntities_ShouldInsertSuccessfully()
    {
        // Arrange
        var entities = new List<CriterionFeedbackEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CriterionId = "criterion1",
                SubmissionId = "submission1",
                Score = 85,
                Status = "Draft",
                FeedbackType = "Criterion",
                IsActive = true
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CriterionId = "criterion2",
                SubmissionId = "submission2",
                Score = 90,
                Status = "Submitted",
                FeedbackType = "Criterion",
                IsActive = true
            }
        };

        // Act
        var result = await _store.CreateBulkCriterionFeedbackAsync(entities, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(2, result.ProcessedCount);

        // Verify entities were inserted with business logic applied
        var insertedEntities = await _context.CriterionFeedbacks.ToListAsync();
        Assert.Equal(2, insertedEntities.Count);
        Assert.All(insertedEntities, entity =>
        {
            Assert.NotNull(entity.CreatedAt);
            Assert.NotNull(entity.UpdatedAt);
            Assert.True(entity.IsActive);
            Assert.Equal("Criterion", entity.FeedbackType);
        });
    }

    [Fact]
    public async Task CreateBulkCriterionFeedbackAsync_WithInvalidEntities_ShouldReturnValidationErrors()
    {
        // Arrange
        var entities = new List<CriterionFeedbackEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CriterionId = "", // Invalid - empty
                SubmissionId = "submission1",
                Score = -10, // Invalid - negative
                Status = "Draft"
            }
        };

        // Act
        var result = await _store.CreateBulkCriterionFeedbackAsync(entities, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Equal(0, result.ProcessedCount);
        Assert.Contains("CriterionId boş olamaz", result.ErrorMessage);
        Assert.Contains("Score 0-100 arasında olmalı", result.ErrorMessage);
    }

    [Fact]
    public async Task BulkUpdateAsync_WithValidEntities_ShouldUpdateSuccessfully()
    {
        // Arrange
        var entities = new List<SubmissionFeedbackEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                SubmissionId = "submission1",
                FeedbackType = "General",
                Score = 85,
                Status = "Draft",
                IsActive = true
            }
        };

        await _context.SubmissionFeedbacks.AddRangeAsync(entities);
        await _context.SaveChangesAsync();

        // Update entity
        entities[0].Score = 95;
        entities[0].Status = "Approved";

        // Act
        var result = await _store.BulkUpdateAsync(entities, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(1, result.ProcessedCount);

        // Verify entity was updated
        var updatedEntity = await _context.SubmissionFeedbacks.FirstAsync();
        Assert.Equal(95, updatedEntity.Score);
        Assert.Equal("Approved", updatedEntity.Status);
        Assert.NotNull(updatedEntity.UpdatedAt);
    }

    [Fact]
    public async Task BulkDeleteAsync_WithValidEntities_ShouldDeleteSuccessfully()
    {
        // Arrange
        var entities = new List<SubmissionFeedbackEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                SubmissionId = "submission1",
                FeedbackType = "General",
                Score = 85,
                Status = "Draft",
                IsActive = true
            }
        };

        await _context.SubmissionFeedbacks.AddRangeAsync(entities);
        await _context.SaveChangesAsync();

        // Act
        var result = await _store.BulkDeleteAsync(entities, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(1, result.ProcessedCount);

        // Verify entity was deleted
        var remainingEntities = await _context.SubmissionFeedbacks.ToListAsync();
        Assert.Empty(remainingEntities);
    }

    [Fact]
    public async Task BulkDeleteAsync_WithApprovedEntity_ShouldReturnValidationErrors()
    {
        // Arrange
        var entities = new List<SubmissionFeedbackEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                SubmissionId = "submission1",
                FeedbackType = "General",
                Score = 85,
                Status = "Approved", // Cannot delete approved entities
                IsActive = true
            }
        };

        // Act
        var result = await _store.BulkDeleteAsync(entities, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Equal(0, result.ProcessedCount);
        Assert.Contains("Approved feedback silinemez", result.ErrorMessage);
    }

    [Fact]
    public async Task CreateBulkFeedbackAsync_WithEmptyList_ShouldReturnZeroProcessed()
    {
        // Arrange
        var emptyList = new List<SubmissionFeedbackEntity>();

        // Act
        var result = await _store.CreateBulkFeedbackAsync(emptyList, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(0, result.ProcessedCount);
    }

    [Fact]
    public async Task BulkOperations_ShouldLogAppropriateMessages()
    {
        // Arrange
        var entities = new List<SubmissionFeedbackEntity>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                SubmissionId = "submission1",
                FeedbackType = "General",
                Score = 85,
                Status = "Draft"
            }
        };

        // Act
        await _store.CreateBulkFeedbackAsync(entities, CancellationToken.None);

        // Assert - Verify logging calls
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("bulk feedback create başlatılıyor")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);

        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("bulk feedback create tamamlandı")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task BusinessLogic_ShouldApplyCorrectly()
    {
        // Arrange
        var entity = new SubmissionFeedbackEntity
        {
            Id = Guid.NewGuid().ToString(),
            SubmissionId = "submission1",
            FeedbackType = "", // Should be set to "General"
            Score = 150, // Should be corrected to 100
            Status = "InvalidStatus" // Should be corrected to "Draft"
        };

        var entities = new List<SubmissionFeedbackEntity> { entity };

        // Act
        var result = await _store.CreateBulkFeedbackAsync(entities, CancellationToken.None);

        // Assert
        Assert.True(result.Success);

        var insertedEntity = await _context.SubmissionFeedbacks.FirstAsync();
        Assert.Equal("General", insertedEntity.FeedbackType); // Corrected by business logic
        Assert.Equal(100, insertedEntity.Score); // Corrected by business logic
        Assert.Equal("Draft", insertedEntity.Status); // Corrected by business logic
        Assert.True(insertedEntity.IsActive); // Set by business logic
    }

    public void Dispose()
    {
        _context?.Dispose();
    }
}
