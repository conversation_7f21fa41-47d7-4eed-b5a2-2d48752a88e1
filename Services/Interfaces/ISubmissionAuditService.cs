using AcademicPerformance.Models.AcademicPerformanceDbContextModels;

namespace AcademicPerformance.Services.Interfaces
{
    /// <summary>
    /// Submission audit service interface - Epic 4 Task 3.1
    /// Submission işlemlerinin audit trail'inin tutulması
    /// </summary>
    public interface ISubmissionAuditService
    {
        /// <summary>
        /// Audit kaydı oluştur
        /// </summary>
        /// <param name="submissionId">Submission AutoIncrement ID'si</param>
        /// <param name="action">Yapılan action</param>
        /// <param name="performedByUserId">Action'ı gerçekleştiren kullanıcı ID'si</param>
        /// <param name="comments"><PERSON><PERSON><PERSON> (opsiyonel)</param>
        /// <param name="oldValue"><PERSON><PERSON> de<PERSON>er (opsiyonel)</param>
        /// <param name="newValue"><PERSON><PERSON> (opsiyonel)</param>
        /// <param name="entityType">Entity tipi (opsiyonel)</param>
        /// <param name="entityId">Entity ID'si (opsiyonel)</param>
        /// <param name="category">Kategori (opsiyonel)</param>
        /// <param name="metadata">Ek metadata (opsiyonel)</param>
        /// <returns>Audit kaydı başarılı mı?</returns>
        Task<bool> LogAsync(
            int submissionId,
            string action,
            string performedByUserId,
            string? comments = null,
            string? oldValue = null,
            string? newValue = null,
            string? entityType = null,
            string? entityId = null,
            string? category = null,
            object? metadata = null);

        /// <summary>
        /// Status change audit kaydı oluştur
        /// </summary>
        /// <param name="submissionId">Submission AutoIncrement ID'si</param>
        /// <param name="oldStatus">Eski status</param>
        /// <param name="newStatus">Yeni status</param>
        /// <param name="performedByUserId">Action'ı gerçekleştiren kullanıcı ID'si</param>
        /// <param name="comments">Yorumlar (opsiyonel)</param>
        /// <returns>Audit kaydı başarılı mı?</returns>
        Task<bool> LogStatusChangeAsync(
            int submissionId,
            string oldStatus,
            string newStatus,
            string performedByUserId,
            string? comments = null);

        /// <summary>
        /// File operation audit kaydı oluştur
        /// </summary>
        /// <param name="submissionId">Submission AutoIncrement ID'si</param>
        /// <param name="fileId">File ID'si</param>
        /// <param name="fileName">Dosya adı</param>
        /// <param name="operation">File operation (Upload, Delete, Download)</param>
        /// <param name="performedByUserId">Action'ı gerçekleştiren kullanıcı ID'si</param>
        /// <param name="fileSize">Dosya boyutu (opsiyonel)</param>
        /// <returns>Audit kaydı başarılı mı?</returns>
        Task<bool> LogFileOperationAsync(
            int submissionId,
            string fileId,
            string fileName,
            string operation,
            string performedByUserId,
            long? fileSize = null);

        /// <summary>
        /// Criterion data operation audit kaydı oluştur
        /// </summary>
        /// <param name="submissionId">Submission AutoIncrement ID'si</param>
        /// <param name="criterionLinkId">Criterion link ID'si</param>
        /// <param name="operation">Data operation (Input, Update, Delete)</param>
        /// <param name="performedByUserId">Action'ı gerçekleştiren kullanıcı ID'si</param>
        /// <param name="oldData">Eski data (opsiyonel)</param>
        /// <param name="newData">Yeni data (opsiyonel)</param>
        /// <returns>Audit kaydı başarılı mı?</returns>
        Task<bool> LogCriterionDataOperationAsync(
            int submissionId,
            string criterionLinkId,
            string operation,
            string performedByUserId,
            object? oldData = null,
            object? newData = null);

        /// <summary>
        /// Submission'a ait audit kayıtlarını getir
        /// </summary>
        /// <param name="submissionId">Submission AutoIncrement ID'si</param>
        /// <param name="pageSize">Sayfa boyutu (default: 50)</param>
        /// <param name="pageNumber">Sayfa numarası (default: 1)</param>
        /// <returns>Audit kayıtları</returns>
        Task<List<SubmissionAuditEntity>> GetAuditTrailAsync(
            int submissionId,
            int pageSize = 50,
            int pageNumber = 1);

        /// <summary>
        /// Kullanıcının audit kayıtlarını getir
        /// </summary>
        /// <param name="userId">Kullanıcı ID'si</param>
        /// <param name="pageSize">Sayfa boyutu (default: 50)</param>
        /// <param name="pageNumber">Sayfa numarası (default: 1)</param>
        /// <returns>Audit kayıtları</returns>
        Task<List<SubmissionAuditEntity>> GetUserAuditTrailAsync(
            string userId,
            int pageSize = 50,
            int pageNumber = 1);
    }
}
